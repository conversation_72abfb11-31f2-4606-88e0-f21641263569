{"translation": {"agents": {"add.button": "新增到助手", "add.knowledge_base": "知識庫", "add.knowledge_base.placeholder": "選擇知識庫", "add.name": "名稱", "add.name.placeholder": "輸入名稱", "add.prompt": "提示詞", "add.prompt.placeholder": "輸入提示詞", "add.title": "建立智慧代理人", "delete.popup.content": "確定要刪除此智慧代理人嗎？", "edit.message.add.title": "新增", "edit.message.assistant.placeholder": "輸入助手訊息", "edit.message.assistant.title": "助手", "edit.message.empty.content": "會話輸入內容不能為空", "edit.message.group.title": "訊息分組", "edit.message.title": "預設訊息", "edit.message.user.placeholder": "輸入使用者訊息", "edit.message.user.title": "使用者", "edit.model.select.title": "選擇模型", "edit.settings.hide_preset_messages": "隱藏預設訊息", "edit.title": "編輯智慧代理人", "manage.title": "管理智慧代理人", "my_agents": "我的智慧代理人", "search.no_results": "沒有找到相關智慧代理人", "sorting.title": "排序", "tag.agent": "智慧代理人", "tag.default": "預設", "tag.new": "新增", "tag.system": "系統", "title": "智慧代理人"}, "assistants": {"title": "助手", "abbr": "助手", "settings.title": "助手設定", "clear.content": "清空話題會刪除助手下所有主題和檔案，確定要繼續嗎？", "clear.title": "清空話題", "copy.title": "複製助手", "delete.content": "刪除助手會刪除所有該助手下的話題和檔案，確定要繼續嗎？", "delete.title": "刪除助手", "edit.title": "編輯助手", "save.success": "儲存成功", "save.title": "儲存到智慧代理人", "icon.type": "助手圖示", "search": "搜尋助手...", "settings.mcp": "MCP 伺服器", "settings.mcp.enableFirst": "請先在 MCP 設定中啟用此伺服器", "settings.mcp.title": "MCP 設定", "settings.mcp.noServersAvailable": "無可用 MCP 伺服器。請在設定中新增伺服器", "settings.mcp.description": "預設啟用的 MCP 伺服器", "settings.default_model": "預設模型", "settings.knowledge_base": "知識庫設定", "settings.model": "模型設定", "settings.preset_messages": "預設訊息", "settings.prompt": "提示詞設定", "settings.reasoning_effort": "思維鏈長度", "settings.reasoning_effort.high": "長", "settings.reasoning_effort.low": "短", "settings.reasoning_effort.medium": "中", "settings.reasoning_effort.off": "關", "settings.reasoning_effort.tip": "僅支援 OpenAI o-series、Anthropic 和 Grok 推理模型", "settings.more": "助手設定"}, "auth": {"error": "自動取得金鑰失敗，請手動取得", "get_key": "取得", "get_key_success": "自動取得金鑰成功", "login": "登入", "oauth_button": "使用{{provider}}登入"}, "backup": {"confirm": "確定要備份資料嗎？", "confirm.button": "選擇備份位置", "content": "備份全部資料，包括聊天記錄、設定、知識庫等全部資料。請注意，備份過程可能需要一些時間，感謝您的耐心等待。", "progress": {"completed": "備份完成", "compressing": "壓縮檔案...", "copying_files": "複製檔案... {{progress}}%", "preparing": "準備備份...", "title": "備份進度", "writing_data": "寫入資料..."}, "title": "資料備份"}, "button": {"add": "新增", "added": "已新增", "collapse": "折疊", "manage": "管理", "select_model": "選擇模型", "show.all": "顯示全部", "update_available": "有可用更新"}, "chat": {"add.assistant.title": "新增助手", "artifacts.button.download": "下載", "artifacts.button.openExternal": "外部瀏覽器開啟", "artifacts.button.preview": "預覽", "artifacts.preview.openExternal.error.content": "外部瀏覽器開啟出錯", "assistant.search.placeholder": "搜尋", "deeply_thought": "已深度思考（用時 {{secounds}} 秒）", "default.description": "你好，我是預設助手。你可以立即開始與我聊天。", "default.name": "預設助手", "default.topic.name": "預設話題", "history": {"assistant_node": "助手", "click_to_navigate": "點擊跳轉到對應訊息", "coming_soon": "聊天工作流圖表即將上線", "no_messages": "沒有找到訊息", "start_conversation": "開始對話以查看聊天流程圖", "title": "聊天歷史", "user_node": "用戶", "view_full_content": "查看完整內容"}, "input.auto_resize": "自動調整高度", "input.clear": "清除 {{Command}}", "input.clear.content": "您想要清除目前話題的所有訊息嗎？", "input.clear.title": "清除所有訊息？", "input.collapse": "折疊", "input.context_count.tip": "上下文數 / 最大上下文數", "input.estimated_tokens.tip": "預估 Token 數", "input.expand": "展開", "input.file_not_supported": "模型不支援此檔案類型", "input.generate_image": "生成圖片", "input.generate_image_not_supported": "模型不支援生成圖片", "input.knowledge_base": "知識庫", "input.new.context": "清除上下文 {{Command}}", "input.new_topic": "新話題 {{Command}}", "input.pause": "暫停", "input.placeholder": "在此輸入您的訊息...", "input.send": "傳送", "input.settings": "設定", "input.topics": " 話題 ", "input.translate": "翻譯成{{target_language}}", "input.upload": "上傳圖片或文件", "input.upload.document": "上傳文件（模型不支援圖片）", "input.web_search": "開啟網路搜尋", "input.web_search.button.ok": "去設定", "input.web_search.enable": "開啟網路搜尋", "input.web_search.enable_content": "需要先在設定中開啟網路搜尋", "message.new.branch": "分支", "message.new.branch.created": "新分支已建立", "message.new.context": "新上下文", "message.quote": "引用", "message.regenerate.model": "切換模型", "message.useful": "有用", "navigation": {"first": "已經是第一條訊息", "history": "聊天歷史", "last": "已經是最後一條訊息", "next": "下一條訊息", "prev": "上一條訊息", "top": "回到頂部", "bottom": "回到底部", "close": "關閉"}, "resend": "重新傳送", "save": "儲存", "settings.code_collapsible": "程式碼區塊可折疊", "settings.code_wrappable": "程式碼區塊可自動換行", "settings.code_cacheable": "程式碼區塊快取", "settings.code_cacheable.tip": "快取程式碼區塊可以減少長程式碼區塊的渲染時間，但會增加記憶體使用量", "settings.code_cache_max_size": "快取上限", "settings.code_cache_max_size.tip": "允許快取的字元數上限（千字符），按照高亮後的程式碼計算。高亮後的程式碼長度相比純文字會長很多。", "settings.code_cache_ttl": "快取期限", "settings.code_cache_ttl.tip": "快取的存活時間（分鐘）", "settings.code_cache_threshold": "快取門檻", "settings.code_cache_threshold.tip": "允許快取的最小程式碼長度（千字符），超過門檻的程式碼區塊才會被快取", "settings.context_count": "上下文", "settings.context_count.tip": "在上下文中保留的前幾則訊息。", "settings.max": "最大", "settings.max_tokens": "啟用最大 Token 限制", "settings.max_tokens.confirm": "啟用訊息長度限制", "settings.max_tokens.confirm_content": "啟用訊息長度限制後，單次互動所用的最大 Token 數，會影響返回結果的長度。要根據模型上下文限制來設定，否則會發生錯誤。", "settings.max_tokens.tip": "模型可以生成的最大 Token 數。要根據模型上下文限制來設定，否則會發生錯誤。", "settings.reset": "重設", "settings.set_as_default": "設為預設助手", "settings.show_line_numbers": "程式碼顯示行號", "settings.temperature": "溫度", "settings.temperature.tip": "模型產生文字的隨機程度。數值越高，回應內容越具多樣性、創意性及隨機性；設定為 0 則會依據事實回答。一般聊天建議設定為 0.7", "settings.thought_auto_collapse": "思考內容自動折疊", "settings.thought_auto_collapse.tip": "思考結束後思考內容自動折疊", "settings.top_p": "Top-P", "settings.top_p.tip": "模型生成文字的隨機程度。值越小，AI 生成的內容越單調，也越容易理解；值越大，AI 回覆的詞彙範圍越大，越多樣化", "suggestions.title": "建議的問題", "thinking": "思考中", "topics.auto_rename": "自動重新命名", "topics.clear.title": "清空訊息", "topics.copy.image": "複製為圖片", "topics.copy.md": "複製為 Markdown", "topics.copy.title": "複製", "topics.delete.shortcut": "按住 {{key}} 可直接刪除", "topics.edit.placeholder": "輸入新名稱", "topics.edit.title": "編輯名稱", "topics.export.image": "匯出為圖片", "topics.export.joplin": "匯出到 <PERSON><PERSON><PERSON>", "topics.export.md": "匯出為 Markdown", "topics.export.md.reason": "匯出為 Markdown (包含思考)", "topics.export.notion": "匯出到 Notion", "topics.export.obsidian": "匯出到 Obsidian", "topics.export.obsidian_vault": "保管庫", "topics.export.obsidian_vault_placeholder": "請選擇保管庫名稱", "topics.export.obsidian_path": "路徑", "topics.export.obsidian_path_placeholder": "請選擇路徑", "topics.export.obsidian_atributes": "配置筆記屬性", "topics.export.obsidian_btn": "確定", "topics.export.obsidian_created": "建立時間", "topics.export.obsidian_created_placeholder": "請選擇建立時間", "topics.export.obsidian_export_failed": "匯出失敗", "topics.export.obsidian_export_success": "匯出成功", "topics.export.obsidian_operate": "處理方式", "topics.export.obsidian_operate_append": "追加", "topics.export.obsidian_operate_new_or_overwrite": "新建（如果存在就覆蓋）", "topics.export.obsidian_operate_placeholder": "請選擇處理方式", "topics.export.obsidian_operate_prepend": "前置", "topics.export.obsidian_source": "來源", "topics.export.obsidian_source_placeholder": "請輸入來源", "topics.export.obsidian_tags": "標籤", "topics.export.obsidian_tags_placeholder": "請輸入標籤名稱，多個標籤用英文逗號分隔", "topics.export.obsidian_title": "標題", "topics.export.obsidian_title_placeholder": "請輸入標題", "topics.export.obsidian_title_required": "標題不能為空", "topics.export.obsidian_no_vaults": "未找到Obsidian保管庫", "topics.export.obsidian_loading": "加載中...", "topics.export.obsidian_fetch_error": "獲取Obsidian保管庫失敗", "topics.export.obsidian_fetch_folders_error": "獲取文件夾結構失敗", "topics.export.obsidian_no_vault_selected": "請先選擇一個保管庫", "topics.export.obsidian_select_vault_first": "請先選擇保管庫", "topics.export.obsidian_root_directory": "根目錄", "topics.export.title": "匯出", "topics.export.word": "匯出為 Word", "topics.export.yuque": "匯出到語雀", "topics.list": "話題列表", "topics.move_to": "移動到", "topics.new": "開始新對話", "topics.pinned": "固定話題", "topics.prompt": "話題提示詞", "topics.prompt.edit.title": "編輯話題提示詞", "topics.prompt.tips": "話題提示詞：針對目前話題提供額外的補充提示詞", "topics.title": "話題", "topics.unpinned": "取消固定", "translate": "翻譯", "topics.export.siyuan": "匯出到思源筆記", "topics.export.wait_for_title_naming": "正在生成標題...", "topics.export.title_naming_success": "標題生成成功", "topics.export.title_naming_failed": "標題生成失敗，使用預設標題", "input.translating": "翻譯中...", "input.upload.upload_from_local": "上傳本地文件..."}, "code_block": {"collapse": "折疊", "disable_wrap": "停用自動換行", "enable_wrap": "自動換行", "expand": "展開"}, "common": {"add": "新增", "advanced_settings": "進階設定", "and": "與", "assistant": "智慧代理人", "avatar": "頭像", "back": "返回", "cancel": "取消", "chat": "聊天", "clear": "清除", "close": "關閉", "confirm": "確認", "copied": "已複製", "copy": "複製", "cut": "剪下", "default": "預設", "delete": "刪除", "description": "描述", "docs": "文件", "download": "下載", "duplicate": "複製", "edit": "編輯", "expand": "展開", "collapse": "折疊", "footnote": "引用內容", "footnotes": "引用", "fullscreen": "已進入全螢幕模式，按 F11 結束", "knowledge_base": "知識庫", "language": "語言", "model": "模型", "models": "模型", "more": "更多", "name": "名稱", "paste": "貼上", "prompt": "提示詞", "provider": "供應商", "regenerate": "重新生成", "rename": "重新命名", "reset": "重設", "save": "儲存", "search": "搜尋", "select": "選擇", "topics": "話題", "warning": "警告", "you": "您", "reasoning_content": "已深度思考", "sort": {"pinyin": "按拼音排序", "pinyin.asc": "按拼音升序", "pinyin.desc": "按拼音降序"}}, "docs": {"title": "說明文件"}, "error": {"backup.file_format": "備份檔案格式錯誤", "chat.response": "出現錯誤。如果尚未設定 API 金鑰，請前往設定 > 模型提供者中設定金鑰", "http": {"400": "請求錯誤，請檢查請求參數是否正確。如果修改了模型設定，請重設到預設設定", "401": "身份驗證失敗，請檢查 API 金鑰是否正確", "403": "禁止存取，請檢查是否實名認證，或聯絡供應商商問被禁止原因", "404": "模型不存在或者請求路徑錯誤", "429": "請求過多，請稍後再試", "500": "伺服器錯誤，請稍後再試", "502": "閘道器錯誤，請稍後再試", "503": "服務無法使用，請稍後再試", "504": "閘道器超時，請稍後再試"}, "model.exists": "模型已存在", "no_api_key": "API 金鑰未設定", "provider_disabled": "模型供應商未啟用", "render": {"description": "消息內容渲染失敗，請檢查消息內容格式是否正確", "title": "渲染錯誤"}, "user_message_not_found": "無法找到原始用戶訊息", "unknown": "未知錯誤"}, "export": {"assistant": "助手", "attached_files": "附件", "conversation_details": "會話詳細資訊", "conversation_history": "會話歷史", "created": "建立時間", "last_updated": "最後更新", "messages": "訊息數", "user": "使用者"}, "files": {"actions": "操作", "all": "所有檔案", "count": "個檔案", "created_at": "建立時間", "delete": "刪除", "delete.content": "刪除檔案會刪除檔案在所有訊息中的引用，確定要刪除此檔案嗎？", "delete.paintings.warning": "繪圖中包含該圖片，暫時無法刪除", "delete.title": "刪除檔案", "document": "文件", "edit": "編輯", "file": "檔案", "image": "圖片", "name": "名稱", "open": "開啟", "size": "大小", "text": "文字", "title": "檔案", "type": "類型"}, "gpustack": {"keep_alive_time.description": "模型在記憶體中保持的時間（預設為 5 分鐘）。", "keep_alive_time.placeholder": "分鐘", "keep_alive_time.title": "保持活躍時間", "title": "GPUStack"}, "history": {"continue_chat": "繼續聊天", "locate.message": "定位到訊息", "search.messages": "搜尋所有訊息", "search.placeholder": "搜尋話題或訊息...", "search.topics.empty": "沒有找到相關話題，按 Enter 鍵搜尋所有訊息", "title": "搜尋話題"}, "knowledge": {"add": {"title": "新增知識庫"}, "add_directory": "新增目錄", "add_file": "新增檔案", "add_note": "新增筆記", "add_sitemap": "網站地圖", "add_url": "新增網址", "cancel_index": "取消索引", "chunk_overlap": "重疊大小", "chunk_overlap_placeholder": "預設值（不建議修改）", "chunk_overlap_tooltip": "相鄰文字塊之間重複的內容量，確保分段後的文字塊之間仍然有上下文聯絡，提升模型處理長文字的整體效果", "chunk_size": "分段大小", "chunk_size_change_warning": "分段大小和重疊大小修改只針對新新增的內容有效", "chunk_size_placeholder": "預設值（不建議修改）", "chunk_size_too_large": "分段大小不能超過模型上下文限制（{{max_context}}）", "chunk_size_tooltip": "將文件切割分段，每段的大小，不能超過模型上下文限制", "clear_selection": "清除選擇", "delete": "刪除", "delete_confirm": "確定要刪除此知識庫嗎？", "directories": "目錄", "directory_placeholder": "請輸入目錄路徑", "document_count": "請求文件片段數量", "document_count_default": "預設", "document_count_help": "請求文件片段數量越多，附帶的資訊越多，但需要消耗的 Token 也越多", "drag_file": "拖拽檔案到這裡", "edit_remark": "修改備註", "edit_remark_placeholder": "請輸入備註內容", "empty": "暫無知識庫", "file_hint": "支援 {{file_types}} 格式", "index_all": "索引全部", "index_cancelled": "索引已取消", "index_started": "索引開始", "invalid_url": "無效的網址", "model_info": "模型資訊", "no_bases": "暫無知識庫", "no_match": "不符合知識庫內容", "no_provider": "知識庫模型供應商遺失，該知識庫將不再支援，請重新建立知識庫", "not_set": "未設定", "not_support": "知識庫資料庫引擎已更新，該知識庫將不再支援，請重新建立知識庫", "notes": "筆記", "notes_placeholder": "輸入此知識庫的附加資訊或上下文...", "rename": "重新命名", "search": "搜尋知識庫", "search_placeholder": "輸入查詢內容", "settings": "知識庫設定", "sitemap_placeholder": "請輸入網站地圖 URL", "sitemaps": "網站", "source": "來源", "status": "狀態", "status_completed": "已完成", "status_failed": "失敗", "status_new": "已新增", "status_pending": "等待中", "status_processing": "處理中", "threshold": "匹配度閾值", "threshold_placeholder": "未設定", "threshold_too_large_or_small": "閾值不能大於 1 或小於 0", "threshold_tooltip": "用於衡量使用者問題與知識庫內容之間的相關性（0-1）", "title": "知識庫", "topN": "返回結果數量", "topN__too_large_or_small": "返回結果數量不能大於100或小於1", "topN_placeholder": "未設定", "topN_tooltip": "返回的匹配結果數量，數值越大，匹配結果越多，但消耗的 Token 也越多", "url_added": "網址已新增", "url_placeholder": "請輸入網址，多個網址用換行符號分隔", "urls": "網址", "dimensions": "嵌入維度", "dimensions_size_tooltip": "嵌入維度大小，數值越大，嵌入維度越大，但消耗的 Token 也越多", "dimensions_size_placeholder": "預設值（不建議修改）", "dimensions_size_too_large": "嵌入維度不能超過模型上下文限制（{{max_context}}）"}, "languages": {"arabic": "阿拉伯文", "chinese": "簡體中文", "chinese-traditional": "繁體中文", "english": "英文", "french": "法文", "german": "德文", "italian": "義大利文", "japanese": "日文", "korean": "韓文", "portuguese": "葡萄牙文", "russian": "俄文", "spanish": "西班牙文"}, "lmstudio": {"keep_alive_time.description": "對話後模型在記憶體中保持的時間（預設為 5 分鐘）。", "keep_alive_time.placeholder": "分鐘", "keep_alive_time.title": "保持活躍時間", "title": "LM Studio"}, "mermaid": {"download": {"png": "下載 PNG", "svg": "下載 SVG"}, "resize": {"zoom-in": "放大", "zoom-out": "縮小"}, "tabs": {"preview": "預覽", "source": "原始碼"}, "title": "Mermaid 圖表"}, "message": {"api.check.model.title": "請選擇要偵測的模型", "api.connection.failed": "連接失敗", "api.connection.success": "連接成功", "assistant.added.content": "智慧代理人新增成功", "attachments": {"pasted_image": "剪切板圖片", "pasted_text": "剪切板文件"}, "backup.failed": "備份失敗", "backup.start.success": "開始備份", "backup.success": "備份成功", "chat.completion.paused": "聊天完成已暫停", "citations": "參考文獻", "copied": "已複製！", "copy.failed": "複製失敗", "copy.success": "已複製！", "error.chunk_overlap_too_large": "分段重疊不能大於分段大小", "error.dimension_too_large": "內容尺寸過大", "error.enter.api.host": "請先輸入您的 API 主機地址", "error.enter.api.key": "請先輸入您的 API 金鑰", "error.enter.model": "請先選擇一個模型", "error.enter.name": "請先輸入知識庫名稱", "error.get_embedding_dimensions": "取得嵌入維度失敗", "error.invalid.api.host": "無效的 API 位址", "error.invalid.api.key": "無效的 API 金鑰", "error.invalid.enter.model": "請選擇一個模型", "error.invalid.proxy.url": "無效的代理伺服器 URL", "error.invalid.webdav": "無效的 WebDAV 設定", "error.joplin.export": "匯出 Joplin 失敗，請保持 Joplin 已運行並檢查連接狀態或檢查設定", "error.joplin.no_config": "未設定 Joplin 授權Token 或 URL", "error.invalid.nutstore": "無效的坚果云設定", "error.invalid.nutstore_token": "無效的坚果云 Token", "error.markdown.export.preconf": "導出 Markdown 文件到預先設定的路徑失敗", "error.markdown.export.specified": "導出 Markdown 文件失敗", "error.notion.export": "匯出 Notion 錯誤，請檢查連接狀態並對照文件檢查設定", "error.notion.no_api_key": "未設定 Notion API Key 或 Notion Database ID", "error.yuque.export": "匯出語雀錯誤，請檢查連接狀態並對照文件檢查設定", "error.yuque.no_config": "未設定語雀 Token 或知識庫 Url", "group.delete.content": "刪除分組訊息會刪除使用者提問和所有助手的回答", "group.delete.title": "刪除分組訊息", "ignore.knowledge.base": "網路模式開啟，忽略知識庫", "info.notion.block_reach_limit": "對話過長，自動分頁匯出到 Notion", "loading.notion.exporting_progress": "正在匯出到 Notion ({{current}}/{{total}})...", "loading.notion.preparing": "正在準備匯出到 Notion...", "mention.title": "切換模型回答", "message.code_style": "程式碼風格", "message.delete.content": "確定要刪除此訊息嗎？", "message.delete.title": "刪除訊息", "message.multi_model_style": "多模型回答樣式", "message.multi_model_style.fold": "標籤模式", "message.multi_model_style.fold.compress": "切換到緊湊排列", "message.multi_model_style.fold.expand": "切換到展開排列", "message.multi_model_style.grid": "卡片設定", "message.multi_model_style.horizontal": "橫向排列", "message.multi_model_style.vertical": "縱向堆疊", "message.style": "訊息樣式", "message.style.bubble": "氣泡", "message.style.plain": "簡潔", "regenerate.confirm": "重新生成會覆蓋目前訊息", "reset.confirm.content": "確定要清除所有資料嗎？", "reset.double.confirm.content": "所有資料將會被清除，您確定要繼續嗎？", "reset.double.confirm.title": "資料將會遺失！！！", "restore.failed": "恢復失敗", "restore.success": "恢復成功", "save.success.title": "儲存成功", "searching": "正在搜尋...", "success.joplin.export": "成功匯出到 Jo<PERSON>lin", "success.markdown.export.preconf": "成功導出 Markdown 文件到預先設定的路徑", "success.markdown.export.specified": "成功導出 Markdown 文件", "success.notion.export": "成功匯出到 Notion", "success.yuque.export": "成功匯出到語雀", "switch.disabled": "請等待當前回覆完成", "tools": {"completed": "已完成", "invoking": "調用中", "error": "發生錯誤", "raw": "原始碼", "preview": "預覽"}, "topic.added": "新話題已新增", "upgrade.success.button": "重新啟動", "upgrade.success.content": "請重新啟動程式以完成升級", "upgrade.success.title": "升級成功", "warn.notion.exporting": "正在匯出到 Notion，請勿重複請求匯出！", "warning.rate.limit": "發送過於頻繁，請在 {{seconds}} 秒後再嘗試", "error.siyuan.export": "導出思源筆記失敗，請檢查連接狀態並對照文檔檢查配置", "error.siyuan.no_config": "未配置思源筆記API地址或令牌", "success.siyuan.export": "導出到思源筆記成功", "warn.yuque.exporting": "正在導出語雀，請勿重複請求導出！", "warn.siyuan.exporting": "正在導出到思源筆記，請勿重複請求導出！"}, "minapp": {"popup": {"refresh": "重新整理", "close": "關閉小工具", "minimize": "最小化小工具", "devtools": "開發者工具", "openExternal": "在瀏覽器中開啟", "rightclick_copyurl": "右鍵複製URL", "open_link_external_on": "当前：在瀏覽器中開啟連結", "open_link_external_off": "当前：使用預設視窗開啟連結"}, "sidebar.add.title": "新增到側邊欄", "sidebar.remove.title": "從側邊欄移除", "sidebar.close.title": "關閉", "sidebar.closeall.title": "全部關閉", "sidebar.hide.title": "隱藏小工具", "title": "小工具"}, "miniwindow": {"clipboard": {"empty": "剪貼簿為空"}, "feature": {"chat": "回答此問題", "explanation": "解釋說明", "summary": "內容總結", "translate": "文字翻譯"}, "footer": {"copy_last_message": "按 C 鍵複製", "esc": "按 ESC {{action}}", "esc_back": "返回", "esc_close": "關閉視窗", "backspace_clear": "按 Backspace 清空"}, "input": {"placeholder": {"empty": "詢問 {{model}} 取得幫助...", "title": "你想對下方文字做什麼"}}, "tooltip": {"pin": "窗口置頂"}}, "models": {"add_parameter": "新增參數", "all": "全部", "custom_parameters": "自訂參數", "dimensions": "{{dimensions}} 維", "edit": "編輯模型", "embedding": "嵌入", "embedding_model": "嵌入模型", "embedding_model_tooltip": "在設定->模型服務中點選管理按鈕新增", "function_calling": "函數調用", "no_matches": "無可用模型", "parameter_name": "參數名稱", "parameter_type": {"boolean": "布林值", "json": "JSON", "number": "數字", "string": "文字"}, "pinned": "已固定", "rerank_model": "重排模型", "rerank_model_support_provider": "目前重排序模型僅支持部分服務商 ({{provider}})", "rerank_model_tooltip": "在設定->模型服務中點擊管理按鈕添加", "search": "搜尋模型...", "stream_output": "串流輸出", "type": {"embedding": "嵌入", "free": "免費", "function_calling": "工具", "reasoning": "推理", "rerank": "重排", "select": "選擇模型類型", "text": "文字", "vision": "視覺", "websearch": "網路搜尋"}}, "navbar": {"expand": "伸縮對話框", "hide_sidebar": "隱藏側邊欄", "show_sidebar": "顯示側邊欄"}, "ollama": {"keep_alive_time.description": "對話後模型在記憶體中保持的時間（預設為 5 分鐘）。", "keep_alive_time.placeholder": "分鐘", "keep_alive_time.title": "保持活躍時間", "title": "Ollama"}, "paintings": {"button.delete.image": "刪除繪圖", "button.delete.image.confirm": "確定要刪除此繪圖嗎？", "button.new.image": "新繪圖", "guidance_scale": "引導比例", "guidance_scale_tip": "無分類器指導。控制模型在尋找相關影像時對提示詞的遵循程度", "image.size": "影像尺寸", "inference_steps": "推理步數", "inference_steps_tip": "要執行的推理步數。步數越多，品質越高但耗時越長", "negative_prompt": "反向提示詞", "negative_prompt_tip": "描述你不想在圖片中出現的內容", "number_images": "生成數量", "number_images_tip": "一次生成的圖片數量 (1-4)", "prompt_enhancement": "提示詞增強", "prompt_enhancement_tip": "開啟後將提示重寫為詳細的、適合模型的版本", "prompt_placeholder": "描述你想建立的圖片，例如：一個寧靜的湖泊，夕陽西下，遠處是群山", "regenerate.confirm": "這將覆蓋已生成的圖片，是否繼續？", "seed": "隨機種子", "seed_tip": "相同的種子和提示詞可以生成相似的圖片", "title": "繪圖"}, "plantuml": {"download": {"failed": "下載失敗，請檢查網路", "png": "下載 PNG", "svg": "下載 SVG"}, "tabs": {"preview": "預覽", "source": "原始碼"}, "title": "PlantUML 圖表"}, "prompts": {"explanation": "幫我解釋一下這個概念", "summarize": "幫我總結一下這段話", "title": "你是一名擅長會話的助理，你需要將使用者的會話總結為 10 個字以內的標題，標題語言與使用者的首要語言一致，不要使用標點符號和其他特殊符號"}, "provider": {"aihubmix": "AiHubMix", "alayanew": "Alaya NeW", "anthropic": "Anthropic", "azure-openai": "Azure OpenAI", "baichuan": "百川", "baidu-cloud": "百度雲千帆", "copilot": "GitHub Copilot", "dashscope": "阿里雲百鍊", "deepseek": "深度求索", "dmxapi": "DMXAPI", "doubao": "火山引擎", "fireworks": "Fireworks", "gemini": "Gemini", "gitee-ai": "Gitee AI", "github": "GitHub Models", "gpustack": "GPUStack", "grok": "Grok", "groq": "Groq", "hunyuan": "騰訊混元", "hyperbolic": "Hyperbolic", "infini": "無問芯穹", "jina": "<PERSON><PERSON>", "lmstudio": "LM Studio", "minimax": "MiniMax", "mistral": "<PERSON><PERSON><PERSON>", "modelscope": "ModelScope 魔搭", "moonshot": "月之暗面", "nvidia": "輝達", "o3": "O3", "ocoolai": "ocoolAI", "ollama": "Ollama", "openai": "OpenAI", "openrouter": "OpenRouter", "perplexity": "Perplexity", "ppio": "PPIO 派歐雲", "qwenlm": "QwenLM", "silicon": "SiliconFlow", "stepfun": "<PERSON><PERSON><PERSON>", "tencent-cloud-ti": "騰訊雲 TI", "together": "Together", "xirang": "天翼雲息壤", "yi": "零一萬物", "zhinao": "360 智腦", "zhipu": "智譜 AI", "voyageai": "Voyage AI", "qiniu": "七牛雲"}, "restore": {"confirm": "確定要復原資料嗎？", "confirm.button": "選擇備份檔案", "content": "復原操作將使用備份資料覆蓋目前所有應用程式資料。請注意，復原過程可能需要一些時間，感謝您的耐心等待。", "progress": {"completed": "復原完成", "copying_files": "複製檔案... {{progress}}%", "extracting": "解開備份...", "preparing": "準備復原...", "reading_data": "讀取資料...", "title": "復原進度"}, "title": "資料復原"}, "settings": {"about": "關於與回饋", "about.checkingUpdate": "正在檢查更新...", "about.checkUpdate": "檢查更新", "about.checkUpdate.available": "立即更新", "about.contact.button": "電子郵件", "about.contact.title": "聯絡方式", "about.description": "一款為創作者而生的強大 AI 助手", "about.downloading": "正在下載...", "about.feedback.button": "回饋", "about.feedback.title": "回饋", "about.license.button": "檢視", "about.license.title": "授權", "about.releases.button": "檢視", "about.releases.title": "更新日誌", "about.social.title": "社交帳號", "about.title": "關於我們", "about.updateAvailable": "發現新版本 {{version}}", "about.updateError": "更新錯誤", "about.updateNotAvailable": "您正在使用最新版本", "about.website.button": "網站", "about.website.title": "官方網站", "advanced.auto_switch_to_topics": "自動切換到話題", "advanced.title": "進階設定", "assistant": "預設助手", "assistant.model_params": "模型參數", "assistant.icon.type": "模型圖示類型", "assistant.icon.type.model": "模型圖示", "assistant.icon.type.emoji": "Emoji 表情", "assistant.icon.type.none": "不顯示", "assistant.title": "預設助手", "data": {"app_data": "應用程式資料", "app_knowledge": "知識庫文件", "app_knowledge.button.delete": "刪除檔案", "app_knowledge.remove_all": "刪除知識庫檔案", "app_knowledge.remove_all_confirm": "刪除知識庫文件可以減少儲存空間佔用，但不會刪除知識庫向量化資料，刪除之後將無法開啟原始檔，是否刪除？", "app_knowledge.remove_all_success": "檔案刪除成功", "app_logs": "應用程式日誌", "clear_cache": {"button": "清除快取", "confirm": "清除快取將刪除應用快取資料，包括小工具資料。此操作不可恢復，是否繼續？", "error": "清除快取失敗", "success": "快取清除成功", "title": "清除快取"}, "data.title": "資料目錄", "divider.basic": "基礎數據設定", "divider.cloud_storage": "雲備份設定", "divider.export_settings": "匯出設定", "divider.third_party": "第三方連接", "hour_interval_one": "{{count}} 小時", "hour_interval_other": "{{count}} 小時", "export_menu": {"title": "匯出選單設定", "image": "匯出為圖片", "markdown": "匯出為Markdown", "markdown_reason": "匯出為Markdown（包含思考）", "notion": "匯出到Notion", "yuque": "匯出到語雀", "obsidian": "匯出到Obsidian", "siyuan": "匯出到思源筆記", "joplin": "匯出到<PERSON><PERSON><PERSON>", "docx": "匯出為Word"}, "joplin": {"check": {"button": "檢查", "empty_token": "請先輸入 Jo<PERSON>lin 授權Token", "empty_url": "請先輸入 Joplin 剪輯服務 URL", "fail": "Jo<PERSON>lin 連接驗證失敗", "success": "Jo<PERSON>lin 連接驗證成功"}, "help": "在 Joplin 選項中，啟用剪輯服務（無需安裝瀏覽器外掛），確認埠編號，並複製授權Token", "title": "<PERSON><PERSON><PERSON> 設定", "token": "Jo<PERSON>lin 授權Token", "token_placeholder": "請輸入 Jo<PERSON>lin 授權Token", "url": "Joplin 剪輯服務 URL", "url_placeholder": "http://127.0.0.1:41184/"}, "markdown_export.force_dollar_math.help": "開啟後，匯出Markdown時會強制使用$$來標記LaTeX公式。注意：該項也會影響所有透過Markdown匯出的方式，如Notion、語雀等。", "markdown_export.force_dollar_math.title": "LaTeX公式強制使用$$", "markdown_export.help": "若填入，每次匯出時將自動儲存至該路徑；否則，將彈出儲存對話框。", "markdown_export.path": "預設匯出路徑", "markdown_export.path_placeholder": "匯出路徑", "markdown_export.select": "選擇", "markdown_export.title": "Markdown 匯出", "minute_interval_one": "{{count}} 分鐘", "minute_interval_other": "{{count}} 分鐘", "notion.api_key": "Notion 金鑰", "notion.api_key_placeholder": "請輸入 Notion 金鑰", "notion.auto_split": "匯出對話時自動分頁", "notion.auto_split_tip": "當要匯出的話題過長時自動分頁匯出到 Notion", "notion.check": {"button": "檢查", "empty_api_key": "未設定 API key", "empty_database_id": "未設定 Database ID", "error": "連接異常，請檢查網路及 API key 和 Database ID 是否正確", "fail": "連接失敗，請檢查網路及 API key 和 Database ID 是否正確", "success": "連線成功"}, "notion.database_id": "Notion 資料庫 ID", "notion.database_id_placeholder": "請輸入 Notion 資料庫 ID", "notion.help": "Notion 設定文件", "notion.page_name_key": "頁面標題欄位名稱", "notion.page_name_key_placeholder": "請輸入頁面標題欄位名稱，預設為 Name", "notion.split_size": "自動分頁大小", "notion.split_size_help": "Notion 免費版使用者建議設定為 90，進階版使用者建議設定為 24990，預設值為 90", "notion.split_size_placeholder": "請輸入每頁塊數限制 (預設 90)", "notion.title": "Notion 設定", "title": "資料設定", "webdav": {"autoSync": "自動備份", "autoSync.off": "關閉", "backup.button": "備份到 WebDAV", "backup.modal.filename.placeholder": "請輸入備份文件名", "backup.modal.title": "備份到 WebDAV", "backup.manager.title": "備份數據管理", "backup.manager.refresh": "刷新", "backup.manager.delete.selected": "刪除選中", "backup.manager.delete.text": "刪除", "backup.manager.restore.text": "恢復", "backup.manager.restore.success": "恢復成功，應用將在幾秒後刷新", "backup.manager.restore.error": "恢復失敗", "backup.manager.delete.confirm.title": "確認刪除", "backup.manager.delete.confirm.single": "確定要刪除備份文件 \"{{fileName}}\" 嗎？此操作不可恢復。", "backup.manager.delete.confirm.multiple": "確定要刪除選中的 {{count}} 個備份文件嗎？此操作不可恢復。", "backup.manager.delete.success.single": "刪除成功", "backup.manager.delete.success.multiple": "成功刪除 {{count}} 個備份文件", "backup.manager.delete.error": "刪除失敗", "backup.manager.fetch.error": "獲取備份文件失敗", "backup.manager.select.files.delete": "請選擇要刪除的備份文件", "backup.manager.columns.fileName": "文件名", "backup.manager.columns.modifiedTime": "修改時間", "backup.manager.columns.size": "大小", "backup.manager.columns.actions": "操作", "host": "WebDAV 主機位址", "host.placeholder": "http://localhost:8080", "hour_interval_one": "{{count}} 小時", "hour_interval_other": "{{count}} 小時", "lastSync": "上次備份時間", "minute_interval_one": "{{count}} 分鐘", "minute_interval_other": "{{count}} 分鐘", "noSync": "等待下次備份", "password": "WebDAV 密碼", "path": "WebDAV 路徑", "path.placeholder": "/backup", "restore.button": "從 WebDAV 恢復", "restore.confirm.content": "從 WebDAV 恢復將覆蓋目前資料，是否繼續？", "restore.confirm.title": "復元確認", "restore.content": "從 WebDAV 恢復將覆蓋目前資料，是否繼續？", "restore.modal.select.placeholder": "請選擇要恢復的備份文件", "restore.modal.title": "從 WebDAV 恢復", "restore.title": "從 WebDAV 恢復", "syncError": "備份錯誤", "syncStatus": "備份狀態", "title": "WebDAV", "user": "WebDAV 使用者名稱", "maxBackups": "最大備份數量", "maxBackups.unlimited": "無限制"}, "yuque": {"check": {"button": "檢查", "empty_repo_url": "請先輸入知識庫 URL", "empty_token": "請先輸入語雀 Token", "fail": "語雀連接驗證失敗", "success": "語雀連接驗證成功"}, "help": "取得語雀 Token", "repo_url": "知識庫 URL", "repo_url_placeholder": "https://www.yuque.com/username/xxx", "title": "語雀設定", "token": "語雀 Token", "token_placeholder": "請輸入語雀 Token"}, "obsidian": {"title": "Obsidian 設定", "default_vault": "預設 Obsidian 倉庫", "default_vault_placeholder": "請選擇預設 Obsidian 倉庫", "default_vault_loading": "正在獲取 Obsidian 倉庫...", "default_vault_no_vaults": "未找到 Obsidian 倉庫", "default_vault_fetch_error": "獲取 Obsidian 倉庫失敗", "default_vault_export_failed": "匯出失敗"}, "siyuan": {"title": "思源筆記配置", "api_url": "API地址", "api_url_placeholder": "例如：http://127.0.0.1:6806", "token": "API令牌", "token.help": "在思源筆記->設置->關於中獲取", "token_placeholder": "請輸入思源筆記令牌", "box_id": "筆記本ID", "box_id_placeholder": "請輸入筆記本ID", "root_path": "文檔根路徑", "root_path_placeholder": "例如：/CherryStudio", "check": {"title": "連接檢查", "button": "檢查", "empty_config": "請填寫API地址和令牌", "success": "連接成功", "fail": "連接失敗，請檢查API地址和令牌", "error": "連接異常，請檢查網絡連接"}}, "nutstore": {"title": "堅果雲設定", "isLogin": "已登入", "notLogin": "未登入", "login.button": "登入", "logout.button": "退出登入", "logout.title": "確定要退出堅果雲登入？", "logout.content": "退出後將無法備份至堅果雲和從堅果雲恢復", "checkConnection.name": "檢查連接", "checkConnection.success": "已連接堅果雲", "checkConnection.fail": "堅果雲連接失敗", "username": "堅果雲用戶名", "path": "堅果雲存儲路徑", "path.placeholder": "請輸入堅果雲的存儲路徑", "backup.button": "備份到堅果雲", "restore.button": "從堅果雲恢復", "pathSelector.title": "堅果雲存儲路徑", "pathSelector.return": "返回", "pathSelector.currentPath": "當前路徑", "new_folder.button.confirm": "確定", "new_folder.button.cancel": "取消", "new_folder.button": "新建文件夾"}, "message_title.use_topic_naming.title": "使用話題命名模型為導出的消息創建標題", "message_title.use_topic_naming.help": "此設定會影響所有通過Markdown導出的方式，如Notion、語雀等。"}, "display.assistant.title": "助手設定", "display.custom.css": "自訂 CSS", "display.custom.css.cherrycss": "從 cherrycss.com 取得", "display.custom.css.placeholder": "/* 這裡寫自訂 CSS */", "display.sidebar.chat.hiddenMessage": "助手是基礎功能，不支援隱藏", "display.sidebar.disabled": "隱藏的圖示", "display.sidebar.empty": "把要隱藏的功能從左側拖拽到這裡", "display.sidebar.files.icon": "顯示檔案圖示", "display.sidebar.knowledge.icon": "顯示知識圖示", "display.sidebar.minapp.icon": "顯示小工具圖示", "display.sidebar.painting.icon": "顯示繪圖圖示", "display.sidebar.title": "側邊欄設定", "display.sidebar.translate.icon": "顯示翻譯圖示", "display.sidebar.visible": "顯示的圖示", "display.title": "顯示設定", "display.topic.title": "話題設定", "miniapps": {"title": "小程式設置", "disabled": "隱藏的小程式", "empty": "把要隱藏的小程式從左側拖拽到這裡", "visible": "顯示的小程式", "open_link_external": {"title": "在瀏覽器中打開新視窗連結"}, "cache_settings": "緩存設置", "cache_title": "小程式緩存數量", "cache_description": "設置同時保持活躍狀態的小程式最大數量", "reset_tooltip": "重置為預設值", "display_title": "小程式顯示設置", "sidebar_title": "側邊欄活躍小程式顯示設置", "sidebar_description": "設置側邊欄是否顯示活躍的小程式", "cache_change_notice": "更改將在打開的小程式增減至設定值後生效"}, "font_size.title": "訊息字型大小", "general": "一般設定", "general.avatar.reset": "重設頭像", "general.backup.button": "備份", "general.backup.title": "資料備份與復原", "general.display.title": "顯示設定", "general.emoji_picker": "表情選擇器", "general.image_upload": "圖片上傳", "general.reset.button": "重設", "general.reset.title": "資料重設", "general.restore.button": "復原", "general.title": "一般設定", "general.user_name": "使用者名稱", "general.user_name.placeholder": "輸入您的名稱", "general.view_webdav_settings": "檢視 WebDAV 設定", "input.auto_translate_with_space": "快速敲擊 3 次空格翻譯", "input.show_translate_confirm": "顯示翻譯確認對話框", "input.target_language": "目標語言", "input.target_language.chinese": "簡體中文", "input.target_language.chinese-traditional": "繁體中文", "input.target_language.english": "英文", "input.target_language.japanese": "日文", "input.target_language.russian": "俄文", "launch.onboot": "開機自動啟動", "launch.title": "啟動", "launch.totray": "啟動時最小化到系统匣", "mcp": {"actions": "操作", "active": "啟用", "addError": "添加伺服器失敗", "addServer": "新增伺服器", "addSuccess": "伺服器新增成功", "args": "參數", "argsTooltip": "每個參數佔一行", "baseUrlTooltip": "遠端 URL 地址", "command": "指令", "sse": "伺服器傳送事件 (sse)", "streamableHttp": "可串流的HTTP (streamableHttp)", "stdio": "標準輸入/輸出 (stdio)", "inMemory": "記憶體", "config_description": "設定模型上下文協議伺服器", "deleteError": "刪除伺服器失敗", "deleteSuccess": "伺服器刪除成功", "dependenciesInstall": "安裝相依套件", "dependenciesInstalling": "正在安裝相依套件...", "description": "描述", "duplicateName": "已存在相同名稱的伺服器", "editJson": "編輯JSON", "editServer": "編輯伺服器", "env": "環境變數", "envTooltip": "格式：KEY=value，每行一個", "headers": "請求標頭", "headersTooltip": "HTTP 請求的自定義標頭", "findMore": "更多 MCP", "searchNpx": "搜索 MCP", "install": "安裝", "installError": "安裝相依套件失敗", "installSuccess": "相依套件安裝成功", "jsonFormatError": "JSON格式錯誤", "jsonModeHint": "編輯MCP伺服器配置的JSON表示。保存前請確保格式正確。", "jsonSaveError": "保存JSON配置失敗", "jsonSaveSuccess": "JSON配置已儲存", "missingDependencies": "缺失，請安裝它以繼續", "name": "名稱", "noServers": "未設定伺服器", "newServer": "MCP 伺服器", "npx_list": {"actions": "操作", "description": "描述", "no_packages": "未找到包", "npm": "NPM", "package_name": "包名稱", "scope_placeholder": "輸入 npm 作用域 (例如 @your-org)", "scope_required": "請輸入 npm 作用域", "search": "搜索", "search_error": "搜索失敗", "usage": "用法", "version": "版本"}, "errors": {"32000": "MCP 伺服器啟動失敗，請根據教程檢查參數是否填寫完整"}, "serverPlural": "伺服器", "serverSingular": "伺服器", "title": "MCP 伺服器", "startError": "啟動失敗", "type": "類型", "updateError": "更新伺服器失敗", "updateSuccess": "伺服器更新成功", "url": "URL", "editMcpJson": "編輯 MCP 配置", "installHelp": "獲取安裝幫助", "tabs": {"general": "通用", "description": "描述", "tools": "工具", "prompts": "提示", "resources": "資源"}, "tools": {"inputSchema": "輸入模式", "availableTools": "可用工具", "noToolsAvailable": "無可用工具", "loadError": "獲取工具失敗"}, "prompts": {"availablePrompts": "可用提示", "noPromptsAvailable": "無可用提示", "arguments": "參數", "requiredField": "必填欄位", "genericError": "獲取提示錯誤", "loadError": "獲取提示失敗"}, "resources": {"noResourcesAvailable": "無可用資源", "availableResources": "可用資源", "uri": "URI", "mimeType": "MIME類型", "size": "大小", "blob": "二進位數據", "blobInvisible": "隱藏二進位數據", "text": "文字"}, "deleteServer": "刪除伺服器", "deleteServerConfirm": "確定要刪除此伺服器嗎？", "registry": "套件管理源", "registryTooltip": "選擇用於安裝套件的源，以解決預設源的網路問題。", "registryDefault": "預設", "not_support": "不支援此模型", "user": "用戶", "system": "系統", "types": {"inMemory": "內置", "sse": "SSE", "streamableHttp": "流式", "stdio": "STDIO"}, "sync": {"title": "同步伺服器", "selectProvider": "選擇提供者：", "discoverMcpServers": "發現MCP伺服器", "discoverMcpServersDescription": "訪問平台以發現可用的MCP伺服器", "getToken": "獲取 API 令牌", "getTokenDescription": "從您的帳戶獲取個人 API 令牌", "setToken": "輸入您的令牌", "tokenRequired": "需要 API 令牌", "tokenPlaceholder": "在此輸入 API 令牌", "button": "同步", "error": "同步MCP伺服器出錯", "success": "同步MCP伺服器成功", "unauthorized": "同步未授權", "noServersAvailable": "無可用的 MCP 伺服器"}}, "messages.divider": "訊息間顯示分隔線", "messages.grid_columns": "訊息網格展示列數", "messages.grid_popover_trigger": "網格詳細資訊觸發", "messages.grid_popover_trigger.click": "點選顯示", "messages.grid_popover_trigger.hover": "停留顯示", "messages.input.paste_long_text_as_file": "將長文字貼上為檔案", "messages.input.paste_long_text_threshold": "長文字長度", "messages.input.send_shortcuts": "傳送快捷鍵", "messages.input.show_estimated_tokens": "顯示預估 Token 數", "messages.input.title": "輸入設定", "messages.input.enable_quick_triggers": "啟用 '/' 和 '@' 觸發快捷選單", "messages.input.enable_delete_model": "啟用刪除鍵刪除模型/附件", "messages.markdown_rendering_input_message": "Markdown 渲染輸入訊息", "messages.math_engine": "數學公式引擎", "messages.math_engine.none": "無", "messages.metrics": "首字延遲 {{time_first_token_millsec}}ms | 每秒 {{token_speed}} tokens", "messages.model.title": "模型設定", "messages.navigation": "訊息導航", "messages.navigation.anchor": "對話錨點", "messages.navigation.buttons": "上下按鈕", "messages.navigation.none": "不顯示", "messages.title": "訊息設定", "messages.use_serif_font": "使用襯線字型", "model": "預設模型", "models.add.add_model": "新增模型", "models.add.group_name": "群組名稱", "models.add.group_name.placeholder": "選填，例如 ChatGPT", "models.add.group_name.tooltip": "選填，例如 ChatGPT", "models.add.model_id": "模型 ID", "models.add.model_id.placeholder": "必填，例如 gpt-3.5-turbo", "models.add.model_id.tooltip": "例如 gpt-3.5-turbo", "models.add.model_name": "模型名稱", "models.add.model_name.placeholder": "選填，例如 GPT-4", "models.check.all": "所有", "models.check.all_models_passed": "所有模型檢查通過", "models.check.button_caption": "健康檢查", "models.check.disabled": "關閉", "models.check.enable_concurrent": "並行檢查", "models.check.enabled": "開啟", "models.check.failed": "失敗", "models.check.keys_status_count": "通過：{{count_passed}}個密鑰，失敗：{{count_failed}}個密鑰", "models.check.model_status_summary": "{{provider}}: {{count_passed}} 個模型完成健康檢查（其中 {{count_partial}} 個模型用某些密鑰無法訪問），{{count_failed}} 個模型完全無法訪問。", "models.check.no_api_keys": "未找到API密鑰，請先添加API密鑰。", "models.check.passed": "通過", "models.check.select_api_key": "選擇要使用的API密鑰：", "models.check.single": "單個", "models.check.start": "開始", "models.check.title": "模型健康檢查", "models.check.use_all_keys": "使用密鑰", "models.default_assistant_model": "預設助手模型", "models.default_assistant_model_description": "建立新助手時使用的模型，如果助手未設定模型，則使用此模型", "models.empty": "找不到模型", "models.enable_topic_naming": "話題自動重新命名", "models.manage.add_whole_group": "新增整個分組", "models.manage.remove_whole_group": "移除整個分組", "models.topic_naming_model": "話題命名模型", "models.topic_naming_model_description": "自動命名新話題時使用的模型", "models.topic_naming_model_setting_title": "話題命名模型設定", "models.topic_naming_prompt": "話題命名提示詞", "models.translate_model": "翻譯模型", "models.translate_model_description": "翻譯服務使用的模型", "models.translate_model_prompt_message": "請輸入翻譯模型提示詞", "models.translate_model_prompt_title": "翻譯模型提示詞", "moresetting": "更多設定", "moresetting.check.confirm": "確認勾選", "moresetting.check.warn": "請謹慎勾選此選項，勾選錯誤會導致模型無法正常使用！！！", "moresetting.warn": "風險警告", "provider": {"add.name": "提供者名稱", "add.name.placeholder": "例如：OpenAI", "add.title": "新增提供者", "add.type": "供應商類型", "api.url.preview": "預覽：{{url}}", "api.url.reset": "重設", "api.url.tip": "/結尾忽略 v1 版本，#結尾強制使用輸入位址", "api_host": "API 主機地址", "api_key": "API 金鑰", "api_key.tip": "多個金鑰使用逗號分隔", "api_version": "API 版本", "basic_auth": "HTTP 認證", "basic_auth.tip": "適用於透過伺服器部署的實例（請參閱文檔）。目前僅支援 Basic 方案（RFC7617）。", "basic_auth.user_name": "用戶", "basic_auth.user_name.tip": "留空以停用", "basic_auth.password": "密碼", "basic_auth.password.tip": "", "charge": "餘額充值", "bills": "費用帳單", "check": "檢查", "check_all_keys": "檢查所有金鑰", "check_multiple_keys": "檢查多個 API 金鑰", "oauth": {"button": "使用{{provider}}帳號登入", "description": "本服務由<website>{{provider}}</website>提供", "official_website": "官方網站"}, "copilot": {"auth_failed": "Github Copilot認證失敗", "auth_success": "Github Copilot 認證成功", "auth_success_title": "認證成功", "code_failed": "獲取 Device Code失敗，請重試", "code_generated_desc": "請將設備代碼複製到下面的瀏覽器連結中", "code_generated_title": "獲取設備代碼", "confirm_login": "過度使用可能會導致您的 Github 帳號遭到封號，請謹慎使用!!!!", "confirm_title": "風險警告", "connect": "連接 <PERSON><PERSON><PERSON>", "custom_headers": "自訂請求標頭", "description": "您的 Github 帳號需要訂閱 Copilot", "expand": "展開", "headers_description": "自訂請求標頭(json格式)", "invalid_json": "JSON 格式錯誤", "login": "登入 <PERSON><PERSON><PERSON>", "logout": "退出 <PERSON><PERSON><PERSON>", "logout_failed": "退出失敗，請重試", "logout_success": "已成功登出", "model_setting": "模型設定", "open_verification_first": "請先點擊上方連結訪問驗證頁面", "rate_limit": "速率限制", "tooltip": "使用 Github Copilot 需要先登入 Github"}, "delete.content": "確定要刪除此提供者嗎？", "delete.title": "刪除提供者", "docs_check": "檢查", "docs_more_details": "檢視更多細節", "get_api_key": "點選這裡取得金鑰", "is_not_support_array_content": "開啟相容模式", "no_models_for_check": "沒有可以被檢查的模型（例如對話模型）", "not_checked": "未檢查", "remove_duplicate_keys": "移除重複金鑰", "remove_invalid_keys": "刪除無效金鑰", "search": "搜尋模型平臺...", "search_placeholder": "搜尋模型 ID 或名稱", "title": "模型提供者", "notes": {"title": "模型備註", "placeholder": "輸入Markdown格式內容...", "markdown_editor_default_value": "預覽區域"}}, "proxy": {"mode": {"custom": "自訂代理伺服器", "none": "不使用代理伺服器", "system": "系統代理伺服器", "title": "代理伺服器模式"}, "title": "代理伺服器設定"}, "proxy.title": "代理伺服器地址", "quickAssistant": {"click_tray_to_show": "點選工具列圖示啟動", "enable_quick_assistant": "啟用快捷助手", "read_clipboard_at_startup": "啟動時讀取剪貼簿", "title": "快捷助手", "use_shortcut_to_show": "右鍵點選工具列圖示或使用快捷鍵啟動"}, "shortcuts": {"action": "操作", "clear_shortcut": "清除快捷鍵", "clear_topic": "清除所有訊息", "copy_last_message": "複製上一則訊息", "key": "按鍵", "mini_window": "快捷助手", "new_topic": "新增話題", "press_shortcut": "按下快捷鍵", "reset_defaults": "重設預設快捷鍵", "reset_defaults_confirm": "確定要重設所有快捷鍵嗎？", "reset_to_default": "重設為預設", "search_message": "搜尋訊息", "show_app": "顯示/隱藏應用程式", "show_settings": "開啟設定", "title": "快速方式", "toggle_new_context": "清除上下文", "toggle_show_assistants": "切換助手顯示", "toggle_show_topics": "切換話題顯示", "zoom_in": "放大介面", "zoom_out": "縮小介面", "zoom_reset": "重設縮放"}, "theme.auto": "自動", "theme.dark": "深色", "theme.light": "淺色", "theme.title": "主題", "theme.window.style.opaque": "不透明視窗", "theme.window.style.title": "視窗樣式", "theme.window.style.transparent": "透明視窗", "title": "設定", "topic.position": "話題位置", "topic.position.left": "左側", "topic.position.right": "右側", "topic.show.time": "顯示話題時間", "tray.onclose": "關閉時最小化到系统匣", "tray.show": "顯示系统匣圖示", "tray.title": "系统匣", "websearch": {"check_success": "驗證成功", "get_api_key": "點選這裡取得金鑰", "search_with_time": "搜尋包含日期", "tavily": {"api_key": "Tavily API 金鑰", "api_key.placeholder": "請輸入 Tavily API 金鑰", "description": "Tavily 是一個為 AI 代理量身訂製的搜尋引擎，提供即時、準確的結果、智慧查詢建議和深入的研究能力", "title": "<PERSON><PERSON>"}, "blacklist": "黑名單", "blacklist_description": "以下網站不會出現在搜索結果中", "search_max_result": "搜尋結果個數", "search_result_default": "預設", "check": "檢查", "search_provider": "搜尋服務商", "search_provider_placeholder": "選擇一個搜尋服務商", "no_provider_selected": "請選擇搜索服務商後再檢查", "check_failed": "驗證失敗", "blacklist_tooltip": "匹配模式: *://*.example.com/*\n正则表达式: /example\\.(net|org)/", "subscribe": "黑名單訂閱", "subscribe_update": "更新", "subscribe_add": "添加訂閱", "subscribe_url": "訂閱源地址", "subscribe_name": "替代名稱", "subscribe_name.placeholder": "當下載的訂閱源沒有名稱時所使用的替代名稱", "subscribe_add_success": "訂閱源添加成功!", "subscribe_delete": "刪除", "title": "網路搜尋", "overwrite": "覆蓋搜尋服務商", "overwrite_tooltip": "強制使用搜尋服務商而不是大語言模型進行搜尋", "apikey": "API 金鑰", "free": "免費", "content_limit": "內容長度限制", "content_limit_tooltip": "限制搜尋結果的內容長度，超過限制的內容將被截斷。"}, "general.auto_check_update.title": "啟用自動更新", "quickPhrase": {"title": "快捷短語", "add": "新增短語", "edit": "編輯短語", "titleLabel": "標題", "contentLabel": "內容", "titlePlaceholder": "請輸入短語標題", "contentPlaceholder": "請輸入短語內容，支持使用變量，然後按Tab鍵可以快速定位到變量進行修改。比如：\n幫我規劃從${from}到${to}的行程，然後發送到${email}。", "delete": "刪除短語", "deleteConfirm": "刪除後無法復原，是否繼續？"}, "quickPanel": {"title": "快捷選單", "close": "關閉", "select": "選擇", "page": "翻頁", "confirm": "確認", "back": "後退", "forward": "前進", "multiple": "多選"}, "privacy": {"title": "隱私設定", "enable_privacy_mode": "匿名發送錯誤報告和資料統計"}}, "translate": {"any.language": "任意語言", "button.translate": "翻譯", "close": "關閉", "confirm": {"content": "翻譯後將覆蓋原文，是否繼續？", "title": "翻譯確認"}, "error.failed": "翻譯失敗", "error.not_configured": "翻譯模型未設定", "history": {"clear": "清空歷史", "clear_description": "清空歷史將刪除所有翻譯歷史記錄，是否繼續？", "delete": "刪除", "empty": "翻譯歷史為空", "title": "翻譯歷史"}, "input.placeholder": "輸入文字進行翻譯", "output.placeholder": "翻譯", "processing": "翻譯中...", "scroll_sync.disable": "關閉滾動同步", "scroll_sync.enable": "開啟滾動同步", "title": "翻譯", "tooltip.newline": "換行", "menu": {"description": "對當前輸入框內容進行翻譯"}}, "tray": {"quit": "結束", "show_mini_window": "快捷助手", "show_window": "顯示視窗"}, "words": {"knowledgeGraph": "知識圖譜", "quit": "結束", "show_window": "顯示視窗", "visualization": "視覺化"}}}