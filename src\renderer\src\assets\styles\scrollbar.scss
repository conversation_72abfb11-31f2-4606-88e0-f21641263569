:root {
  --color-scrollbar-thumb: rgba(255, 255, 255, 0.15);
  --color-scrollbar-thumb-hover: rgba(255, 255, 255, 0.2);
  --color-scrollbar-thumb-right: rgba(255, 255, 255, 0.18);
  --color-scrollbar-thumb-right-hover: rgba(255, 255, 255, 0.25);
}

body[theme-mode='light'] {
  --color-scrollbar-thumb: rgba(0, 0, 0, 0.15);
  --color-scrollbar-thumb-hover: rgba(0, 0, 0, 0.2);
  --color-scrollbar-thumb-right: rgba(0, 0, 0, 0.18);
  --color-scrollbar-thumb-right-hover: rgba(0, 0, 0, 0.25);
}

/* 全局初始化滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: var(--color-scrollbar-thumb);
  &:hover {
    background: var(--color-scrollbar-thumb-hover);
  }
}

pre::-webkit-scrollbar-thumb {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.08);
  &:hover {
    background: rgba(0, 0, 0, 0.15);
  }
}
