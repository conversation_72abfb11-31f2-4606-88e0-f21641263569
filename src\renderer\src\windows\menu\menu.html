<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Selection Menu</title>
    <style>
      :root {
        --bg-color: rgba(255, 255, 255, 0.95);
        --button-bg: #f5f5f5;
        --button-hover: #e8e8e8;
        --text-color: #333;
        --border-color: rgba(0, 0, 0, 0.06);
      }

      @media (prefers-color-scheme: dark) {
        :root {
          --bg-color: rgba(80, 80, 80, 0.95);
          --button-bg: #2c2c2c;
          --button-hover: #383838;
          --text-color: #e0e0e0;
          --border-color: rgba(255, 255, 255, 0.08);
        }
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        user-select: none;
      }

      body {
        width: 280px;
        height: 40px;
        background: var(--bg-color);
        overflow: hidden;
        display: flex;
        align-items: center;
      }

      .drag-handle {
        width: 20px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 2px;
        -webkit-app-region: drag;
      }

      .drag-handle::before,
      .drag-handle::after {
        content: '';
        width: 2px;
        height: 16px;
        background-color: var(--border-color);
        border-radius: 1px;
      }

      menu {
        display: flex;
        align-items: center;
        height: 40px;
        flex: 1;
        margin-right: 10px;
        gap: 5px;
      }

      button {
        flex: 1;
        min-width: 0;
        height: 32px;
        border: none;
        background: transparent;
        border-radius: 6px;
        cursor: pointer;
        font-size: 13px;
        font-weight: 500;
        color: var(--text-color);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        outline: none;
      }

      button:hover {
        background: var(--button-hover);
      }

      button:active {
        transform: scale(0.95);
      }

      svg {
        width: 16px;
        height: 16px;
        fill: currentColor;
      }
    </style>
  </head>

  <body>
    <div class="drag-handle"></div>
    <menu>
      <button data-action="chat">
        <svg viewBox="0 0 24 24">
          <path d="M20,2H4C2.9,2,2,2.9,2,4v18l4-4h14c1.1,0,2-0.9,2-2V4C22,2.9,21.1,2,20,2z M20,16H6l-2,2V4h16V16z" />
        </svg>
        提问
      </button>
      <button data-action="explanation">
        <svg viewBox="0 0 24 24">
          <path
            d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M13,17h-2v-6h2V17z M13,9h-2V7h2V9z" />
        </svg>
        释义
      </button>
      <button data-action="translate">
        <svg viewBox="0 0 24 24">
          <path d="M6 4h12v2H6zM6 10h12v2H6zM6 16h8v2H6z" />
        </svg>
        翻译
      </button>
      <button data-action="summary">
        <svg viewBox="0 0 24 24">
          <path d="M14,17H4v2h10V17z M20,9H4v2h16V9z M4,15h16v-2H4V15z M4,5v2h16V5H4z" />
        </svg>
        总结
      </button>
    </menu>

    <script>
      document.querySelectorAll('button').forEach((button) => {
        button.addEventListener('click', () => {
          const action = button.getAttribute('data-action')
          window.api.selectionMenu.action(action)
        })
      })
    </script>
  </body>
</html>
