:root {
  /* 莫兰迪色系：使用柔和、低饱和度的颜色 */
  --primary-color: #b6b5a7; /* 莫兰迪灰褐色，用于背景文字 */
  --secondary-color: #9a8f8f; /* 莫兰迪灰棕色，用于标题背景 */
  --accent-color: #c5b4a0; /* 莫兰迪淡棕色，用于强调元素 */
  --background-color: #e8e3de; /* 莫兰迪米色，用于页面背景 */
  --text-color: #5b5b5b; /* 莫兰迪深灰色，用于主要文字 */
  --light-text-color: #8c8c8c; /* 莫兰迪中灰色，用于次要文字 */
  --divider-color: #d1cbc3; /* 莫兰迪浅灰色，用于分隔线 */
}
body,
html {
  margin: 0;
  padding: 0;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--background-color); /* 使用莫兰迪米色作为页面背景 */
  font-family: 'Noto Sans SC', sans-serif;
  color: var(--text-color); /* 使用莫兰迪深灰色作为主要文字颜色 */
}
.card {
  width: 300px;
  height: 500px;
  background-color: #f2ede9; /* 莫兰迪浅米色，用于卡片背景 */
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}
.header {
  background-color: var(--secondary-color); /* 使用莫兰迪灰棕色作为标题背景 */
  color: #f2ede9; /* 浅色文字与深色背景形成对比 */
  padding: 20px;
  text-align: left;
  position: relative;
  z-index: 1;
}
h1 {
  font-family: 'Noto Serif SC', serif;
  font-size: 20px;
  margin: 0;
  font-weight: 700;
}
.content {
  padding: 30px 20px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
.word {
  text-align: left;
  margin-bottom: 20px;
}
.word-main {
  font-family: 'Noto Serif SC', serif;
  font-size: 36px;
  color: var(--text-color); /* 使用莫兰迪深灰色作为主要词汇颜色 */
  margin-bottom: 10px;
  position: relative;
}
.word-main::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -5px;
  width: 50px;
  height: 3px;
  background-color: var(--accent-color); /* 使用莫兰迪淡棕色作为下划线 */
}
.word-sub {
  font-size: 14px;
  color: var(--light-text-color); /* 使用莫兰迪中灰色作为次要文字颜色 */
  margin: 5px 0;
}
.divider {
  width: 100%;
  height: 1px;
  background-color: var(--divider-color); /* 使用莫兰迪浅灰色作为分隔线 */
  margin: 20px 0;
}
.explanation {
  font-size: 18px;
  line-height: 1.6;
  text-align: left;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.quote {
  position: relative;
  padding-left: 20px;
  border-left: 3px solid var(--accent-color); /* 使用莫兰迪淡棕色作为引用边框 */
}
.background-text {
  position: absolute;
  font-size: 150px;
  color: rgba(182, 181, 167, 0.15); /* 使用莫兰迪灰褐色的透明版本作为背景文字 */
  z-index: 0;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: bold;
}
