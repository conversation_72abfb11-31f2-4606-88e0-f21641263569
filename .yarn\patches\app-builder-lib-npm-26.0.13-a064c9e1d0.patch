diff --git a/out/electron/ElectronFramework.js b/out/electron/ElectronFramework.js
index 5a4b4546870ee9e770d5a50d79790d39baabd268..3f0ac05dfd6bbaeaf5f834341a823718bd10f55c 100644
--- a/out/electron/ElectronFramework.js
+++ b/out/electron/ElectronFramework.js
@@ -55,26 +55,27 @@ async function removeUnusedLanguagesIfNeeded(options) {
     if (!wantedLanguages.length) {
         return;
     }
-    const { dir, langFileExt } = getLocalesConfig(options);
+    const { dirs, langFileExt } = getLocalesConfig(options);
     // noinspection SpellCheckingInspection
-    await (0, tiny_async_pool_1.default)(builder_util_1.MAX_FILE_REQUESTS, await (0, fs_extra_1.readdir)(dir), async (file) => {
-        if (!file.endsWith(langFileExt)) {
+    const deletedFiles = async (dir) => {
+        await (0, tiny_async_pool_1.default)(builder_util_1.MAX_FILE_REQUESTS, await (0, fs_extra_1.readdir)(dir), async (file) => {
+            if (!file.endsWith(langFileExt)) {
+                return;
+            }
+            const language = file.substring(0, file.length - langFileExt.length);
+            if (!wantedLanguages.includes(language)) {
+                return fs.rm(path.join(dir, file), { recursive: true, force: true });
+            }
             return;
-        }
-        const language = file.substring(0, file.length - langFileExt.length);
-        if (!wantedLanguages.includes(language)) {
-            return fs.rm(path.join(dir, file), { recursive: true, force: true });
-        }
-        return;
-    });
+        });
+    };
+    await Promise.all(dirs.map(deletedFiles));
     function getLocalesConfig(options) {
         const { appOutDir, packager } = options;
         if (packager.platform === index_1.Platform.MAC) {
-            return { dir: packager.getResourcesDir(appOutDir), langFileExt: ".lproj" };
-        }
-        else {
-            return { dir: path.join(packager.getResourcesDir(appOutDir), "..", "locales"), langFileExt: ".pak" };
+            return { dirs: [packager.getResourcesDir(appOutDir), packager.getMacOsElectronFrameworkResourcesDir(appOutDir)], langFileExt: ".lproj" };
         }
+        return { dirs: [path.join(packager.getResourcesDir(appOutDir), "..", "locales")], langFileExt: ".pak" };
     }
 }
 class ElectronFramework {
diff --git a/out/node-module-collector/index.d.ts b/out/node-module-collector/index.d.ts
index 8e808be0fa0d5971b9f9605c8eb88f71630e34b7..1b97dccd8a150a67c4312d2ba4757960e624045b 100644
--- a/out/node-module-collector/index.d.ts
+++ b/out/node-module-collector/index.d.ts
@@ -2,6 +2,6 @@ import { NpmNodeModulesCollector } from "./npmNodeModulesCollector";
 import { PnpmNodeModulesCollector } from "./pnpmNodeModulesCollector";
 import { detect, PM, getPackageManagerVersion } from "./packageManager";
 import { NodeModuleInfo } from "./types";
-export declare function getCollectorByPackageManager(rootDir: string): Promise<NpmNodeModulesCollector | PnpmNodeModulesCollector>;
+export declare function getCollectorByPackageManager(rootDir: string): Promise<PnpmNodeModulesCollector | NpmNodeModulesCollector>;
 export declare function getNodeModules(rootDir: string): Promise<NodeModuleInfo[]>;
 export { detect, getPackageManagerVersion, PM };
diff --git a/out/platformPackager.d.ts b/out/platformPackager.d.ts
index 2df1ba2725c54c7b0e8fed67ab52e94f0cdb17bc..c7ff756564cfd216d2c7d8f72f367527010c06f9 100644
--- a/out/platformPackager.d.ts
+++ b/out/platformPackager.d.ts
@@ -67,6 +67,7 @@ export declare abstract class PlatformPackager<DC extends PlatformSpecificBuildO
     getElectronSrcDir(dist: string): string;
     getElectronDestinationDir(appOutDir: string): string;
     getResourcesDir(appOutDir: string): string;
+    getMacOsElectronFrameworkResourcesDir(appOutDir: string): string;
     getMacOsResourcesDir(appOutDir: string): string;
     private checkFileInPackage;
     private sanityCheckPackage;
diff --git a/out/platformPackager.js b/out/platformPackager.js
index 6f799ce0d1cdb5f0b18a9c8187b2db84b3567aa9..879248e6c6786d3473e1a80e3930d3a8d0190aab 100644
--- a/out/platformPackager.js
+++ b/out/platformPackager.js
@@ -465,12 +465,13 @@ class PlatformPackager {
         if (this.platform === index_1.Platform.MAC) {
             return this.getMacOsResourcesDir(appOutDir);
         }
-        else if ((0, Framework_1.isElectronBased)(this.info.framework)) {
+        if ((0, Framework_1.isElectronBased)(this.info.framework)) {
             return path.join(appOutDir, "resources");
         }
-        else {
-            return appOutDir;
-        }
+        return appOutDir;
+    }
+    getMacOsElectronFrameworkResourcesDir(appOutDir) {
+        return path.join(appOutDir, `${this.appInfo.productFilename}.app`, "Contents", "Frameworks", "Electron Framework.framework", "Resources");
     }
     getMacOsResourcesDir(appOutDir) {
         return path.join(appOutDir, `${this.appInfo.productFilename}.app`, "Contents", "Resources");
