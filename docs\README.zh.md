<h1 align="center">
  <a href="https://github.com/CherryHQ/cherry-studio/releases">
    <img src="https://github.com/CherryHQ/cherry-studio/blob/main/build/icon.png?raw=true" width="150" height="150" alt="banner" />
  </a>
</h1>
<p align="center">
  <a href="https://github.com/CherryHQ/cherry-studio">English</a> | 中文 | <a href="./README.ja.md">日本語</a><br></p>
<div align="center">
 <a href="https://trendshift.io/repositories/11772" target="_blank"><img src="https://trendshift.io/api/badge/repositories/11772" alt="kangfenmao%2Fcherry-studio | Trendshift" style="width: 250px; height: 55px;" width="250" height="55"/></a>
 <a href="https://www.producthunt.com/posts/cherry-studio?embed=true&utm_source=badge-featured&utm_medium=badge&utm_souce=badge-cherry&#0045;studio" target="_blank"><img src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=496640&theme=light" alt="Cherry&#0032;Studio - AI&#0032;Chatbots&#0044;&#0032;AI&#0032;Desktop&#0032;Client | Product Hunt" style="width: 250px; height: 54px;" width="250" height="54" /></a>
</div>

# 🍒 Cherry Studio

Cherry Studio 是一款支持多个大语言模型（LLM）服务商的桌面客户端，兼容 Windows、Mac 和 Linux 系统。

👏 欢迎加入 [Telegram 群组](https://t.me/CherryStudioAI)｜[Discord](https://discord.gg/wez8HtpxqQ) | [QQ群(575014769)](https://qm.qq.com/q/lo0D4qVZKi)

❤️ 喜欢 Cherry Studio? 点亮小星星 🌟 或 [赞助开发者](sponsor.md)! ❤️

# 📖 使用教程

https://docs.cherry-ai.com

# 🌠 界面

![](https://github.com/user-attachments/assets/082efa42-c4df-4863-a9cb-80435cecce0f)
![](https://github.com/user-attachments/assets/f8411a65-c51f-47d3-9273-62ae384cc6f1)
![](https://github.com/user-attachments/assets/0d235b3e-65ae-45ab-987f-8dbe003c52be)

# 🌟 主要特性

1. **多样化 LLM 服务支持**：

- ☁️ 支持主流 LLM 云服务：OpenAI、Gemini、Anthropic、硅基流动等
- 🔗 集成流行 AI Web 服务：Claude、Peplexity、Poe、腾讯元宝、知乎直答等
- 💻 支持 Ollama、LM Studio 本地模型部署

2. **智能助手与对话**：

- 📚 内置 300+ 预配置 AI 助手
- 🤖 支持自定义创建专属助手
- 💬 多模型同时对话，获得多样化观点

3. **文档与数据处理**：

- 📄 支持文本、图片、Office、PDF 等多种格式
- ☁️ WebDAV 文件管理与数据备份
- 📊 Mermaid 图表可视化
- 💻 代码高亮显示

4. **实用工具集成**：

- 🔍 全局搜索功能
- 📝 话题管理系统
- 🔤 AI 驱动的翻译功能
- 🎯 拖拽排序
- 🔌 小程序支持
- ⚙️ MCP(模型上下文协议) 服务

5. **优质使用体验**：

- 🖥️ Windows、Mac、Linux 跨平台支持
- 📦 开箱即用，无需配置环境
- 🎨 支持明暗主题与透明窗口
- 📝 完整的 Markdown 渲染
- 🤲 便捷的内容分享功能

# 📝 待辦事項

- [x] 快捷弹窗（读取剪贴板、快速提问、解释、翻译、总结）
- [x] 多模型回答对比
- [x] 支持使用服务供应商提供的 SSO 进行登入
- [x] 全部模型支持连网（开发中...）
- [x] 推出第一个正式版
- [x] 错误修复和改进（开发中...）
- [ ] 插件功能（JavaScript）
- [ ] 浏览器插件（划词翻译、总结、新增至知识库）
- [ ] iOS & Android 客户端
- [ ] AI 笔记
- [ ] 语音输入输出（AI 通话）
- [ ] 数据备份支持自定义备份内容

# 🌈 主题

- 主题库：https://cherrycss.com
- Aero 主题：https://github.com/hakadao/CherryStudio-Aero
- PaperMaterial 主题: https://github.com/rainoffallingstar/CherryStudio-PaperMaterial
- 仿Claude 主题: https://github.com/bjl101501/CherryStudio-Claudestyle-dynamic
- 霓虹枫叶字体主题: https://github.com/BoningtonChen/CherryStudio_themes

欢迎 PR 更多主题

# 🖥️ 开发

参考[开发文档](dev.md)

# 🤝 贡献

我们欢迎对 Cherry Studio 的贡献！您可以通过以下方式贡献：

1. **贡献代码**：开发新功能或优化现有代码。
2. **修复错误**：提交您发现的错误修复。
3. **维护问题**：帮助管理 GitHub 问题。
4. **产品设计**：参与设计讨论。
5. **撰写文档**：改进用户手册和指南。
6. **社区参与**：加入讨论并帮助用户。
7. **推广使用**：宣传 Cherry Studio。

## 入门

1. **Fork 仓库**：Fork 并克隆到您的本地机器。
2. **创建分支**：为您的更改创建分支。
3. **提交更改**：提交并推送您的更改。
4. **打开 Pull Request**：描述您的更改和原因。

有关更详细的指南，请参阅我们的 [贡献指南](./CONTRIBUTING.zh.md)。

感谢您的支持和贡献！

## 相关项目

- [one-api](https://github.com/songquanpeng/one-api)：LLM API 管理及分发系统，支持 OpenAI、Azure、Anthropic 等主流模型，统一 API 接口，可用于密钥管理与二次分发。

# 🚀 贡献者

<a href="https://github.com/CherryHQ/cherry-studio/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=kangfenmao/cherry-studio" />
</a>
<br /><br />

# 🌐 社区

[Telegram](https://t.me/CherryStudioAI) | [Email](mailto:<EMAIL>) | [Twitter](https://x.com/kangfenmao)

# ☕ 赞助

[微信赞赏码](sponsor.md)

# 📃 许可证

[LICENSE](../LICENSE)

# ✉️ 联系我们

<EMAIL>

# ⭐️ Star 记录

[![Star History Chart](https://api.star-history.com/svg?repos=kangfenmao/cherry-studio&type=Timeline)](https://star-history.com/#kangfenmao/cherry-studio&Timeline)
