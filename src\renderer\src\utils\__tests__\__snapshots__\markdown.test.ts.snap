// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`markdown > markdown configuration constants > sanitizeSchema matches snapshot 1`] = `
{
  "attributes": {
    "*": [
      "className",
      "style",
      "id",
      "title",
    ],
    "a": [
      "href",
      "target",
      "rel",
    ],
    "circle": [
      "cx",
      "cy",
      "r",
      "fill",
      "stroke",
    ],
    "g": [
      "transform",
      "fill",
      "stroke",
    ],
    "line": [
      "x1",
      "y1",
      "x2",
      "y2",
      "stroke",
    ],
    "path": [
      "d",
      "fill",
      "stroke",
      "strokeWidth",
      "strokeLinecap",
      "strokeLinejoin",
    ],
    "polygon": [
      "points",
      "fill",
      "stroke",
    ],
    "polyline": [
      "points",
      "fill",
      "stroke",
    ],
    "rect": [
      "x",
      "y",
      "width",
      "height",
      "fill",
      "stroke",
    ],
    "svg": [
      "viewBox",
      "width",
      "height",
      "xmlns",
      "fill",
      "stroke",
    ],
    "text": [
      "x",
      "y",
      "fill",
      "textAnchor",
      "dominantBaseline",
    ],
  },
  "tagNames": [
    "style",
    "p",
    "div",
    "span",
    "b",
    "i",
    "strong",
    "em",
    "ul",
    "ol",
    "li",
    "table",
    "tr",
    "td",
    "th",
    "thead",
    "tbody",
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6",
    "blockquote",
    "pre",
    "code",
    "br",
    "hr",
    "svg",
    "path",
    "circle",
    "rect",
    "line",
    "polyline",
    "polygon",
    "text",
    "g",
    "defs",
    "title",
    "desc",
    "tspan",
    "sub",
    "sup",
  ],
}
`;
