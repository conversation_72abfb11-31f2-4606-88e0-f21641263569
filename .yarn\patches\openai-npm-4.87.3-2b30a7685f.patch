diff --git a/core.js b/core.js
index ebb071d31cd5a14792b62814df072c5971e83300..31e1062d4a7f2422ffec79cf96a35dbb69fe89cb 100644
--- a/core.js
+++ b/core.js
@@ -157,7 +157,7 @@ class APIClient {
             Accept: 'application/json',
             'Content-Type': 'application/json',
             'User-Agent': this.getUserAgent(),
-            ...getPlatformHeaders(),
+            // ...getPlatformHeaders(),
             ...this.authHeaders(opts),
         };
     }
diff --git a/core.mjs b/core.mjs
index 9c1a0264dcd73a85de1cf81df4efab9ce9ee2ab7..33f9f1f237f2eb2667a05dae1a7e3dc916f6bfff 100644
--- a/core.mjs
+++ b/core.mjs
@@ -150,7 +150,7 @@ export class APIClient {
             Accept: 'application/json',
             'Content-Type': 'application/json',
             'User-Agent': this.getUserAgent(),
-            ...getPlatformHeaders(),
+            // ...getPlatformHeaders(),
             ...this.authHeaders(opts),
         };
     }
diff --git a/error.mjs b/error.mjs
index 7d19f5578040afa004bc887aab1725e8703d2bac..59ec725b6142299a62798ac4bdedb63ba7d9932c 100644
--- a/error.mjs
+++ b/error.mjs
@@ -36,7 +36,7 @@ export class APIError extends OpenAIError {
         if (!status || !headers) {
             return new APIConnectionError({ message, cause: castToError(errorResponse) });
         }
-        const error = errorResponse?.['error'];
+        const error = errorResponse?.['error'] || errorResponse;
         if (status === 400) {
             return new BadRequestError(status, error, message, headers);
         }
