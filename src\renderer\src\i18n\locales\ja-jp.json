{"translation": {"agents": {"add.button": "アシスタントに追加", "add.knowledge_base": "ナレッジベース", "add.knowledge_base.placeholder": "ナレッジベースを選択", "add.name": "名前", "add.name.placeholder": "名前を入力", "add.prompt": "プロンプト", "add.prompt.placeholder": "プロンプトを入力", "add.title": "エージェントを作成", "delete.popup.content": "このエージェントを削除してもよろしいですか？", "edit.message.add.title": "追加", "edit.message.assistant.placeholder": "アシスタントのメッセージを入力", "edit.message.assistant.title": "アシスタント", "edit.message.empty.content": "会話の入力内容が空です", "edit.message.group.title": "メッセージグループ", "edit.message.title": "プリセットメッセージ", "edit.message.user.placeholder": "ユーザーメッセージを入力", "edit.message.user.title": "ユーザー", "edit.model.select.title": "モデルを選択", "edit.settings.hide_preset_messages": "プリセットメッセージを非表示", "edit.title": "エージェントを編集", "manage.title": "エージェントを管理", "my_agents": "マイエージェント", "search.no_results": "結果が見つかりません", "sorting.title": "並び替え", "tag.agent": "エージェント", "tag.default": "デフォルト", "tag.new": "新規", "tag.system": "システム", "title": "エージェント"}, "assistants": {"title": "アシスタント", "abbr": "アシスタント", "settings.title": "アシスタント設定", "clear.content": "トピックをクリアすると、アシスタント内のすべてのトピックとファイルが削除されます。続行しますか？", "clear.title": "トピックをクリア", "copy.title": "アシスタントをコピー", "delete.content": "アシスタントを削除すると、そのアシスタントのすべてのトピックとファイルが削除されます。削除しますか？", "delete.title": "アシスタントを削除", "edit.title": "アシスタントを編集", "save.success": "保存に成功しました", "save.title": "エージェントに保存", "icon.type": "アシスタントアイコン", "search": "アシスタントを検索...", "settings.mcp": "MCP サーバー", "settings.mcp.enableFirst": "まず MCP 設定でこのサーバーを有効にしてください", "settings.mcp.title": "MCP 設定", "settings.mcp.noServersAvailable": "利用可能な MCP サーバーがありません。設定でサーバーを追加してください", "settings.mcp.description": "デフォルトで有効な MCP サーバー", "settings.default_model": "デフォルトモデル", "settings.knowledge_base": "ナレッジベース設定", "settings.model": "モデル設定", "settings.preset_messages": "プリセットメッセージ", "settings.prompt": "プロンプト設定", "settings.reasoning_effort": "思考連鎖の長さ", "settings.reasoning_effort.high": "長い", "settings.reasoning_effort.low": "短い", "settings.reasoning_effort.medium": "中程度", "settings.reasoning_effort.off": "オフ", "settings.reasoning_effort.tip": "OpenAI o-series、Anthropic、および Grok の推論モデルのみサポート", "settings.more": "アシスタント設定"}, "auth": {"error": "APIキーの自動取得に失敗しました。手動で取得してください", "get_key": "取得", "get_key_success": "APIキーの自動取得に成功しました", "login": "認証", "oauth_button": "{{provider}}で認証"}, "backup": {"confirm": "データをバックアップしますか？", "confirm.button": "バックアップ位置を選択", "content": "バックアップ操作はすべてのアプリデータを含むため、時間がかかる場合があります。", "progress": {"completed": "バックアップ完了", "compressing": "圧縮中...", "copying_files": "ファイルコピー中... {{progress}}%", "preparing": "バックアップ準備中...", "title": "バックアップ進捗", "writing_data": "データ書き込み中..."}, "title": "データバックアップ"}, "button": {"add": "追加", "added": "追加済み", "collapse": "折りたたむ", "manage": "管理", "select_model": "モデルを選択", "show.all": "すべて表示", "update_available": "更新可能"}, "chat": {"add.assistant.title": "アシスタントを追加", "artifacts.button.download": "ダウンロード", "artifacts.button.openExternal": "外部ブラウザで開く", "artifacts.button.preview": "プレビュー", "artifacts.preview.openExternal.error.content": "外部ブラウザの起動に失敗しました。", "assistant.search.placeholder": "検索", "deeply_thought": "深く考えています（{{secounds}} 秒）", "default.description": "こんにちは、私はデフォルトのアシスタントです。すぐにチャットを始められます。", "default.name": "デフォルトアシスタント", "default.topic.name": "デフォルトトピック", "history": {"assistant_node": "アシスタント", "click_to_navigate": "メッセージに移動", "coming_soon": "チャットワークフロー図がすぐに登場します", "no_messages": "メッセージが見つかりませんでした", "start_conversation": "チャットを開始してチャットワークフロー図を確認してください", "title": "チャット履歴", "user_node": "ユーザー", "view_full_content": "完全な内容を表示"}, "input.auto_resize": "高さを自動調整", "input.clear": "クリア {{Command}}", "input.clear.content": "現在のトピックのすべてのメッセージをクリアしますか？", "input.clear.title": "すべてのメッセージをクリアしますか？", "input.collapse": "折りたたむ", "input.context_count.tip": "コンテキスト数 / 最大コンテキスト数", "input.estimated_tokens.tip": "推定トークン数", "input.expand": "展開", "input.file_not_supported": "モデルはこのファイルタイプをサポートしません", "input.generate_image": "画像を生成する", "input.generate_image_not_supported": "モデルは画像の生成をサポートしていません。", "input.knowledge_base": "ナレッジベース", "input.new.context": "コンテキストをクリア {{Command}}", "input.new_topic": "新しいトピック {{Command}}", "input.pause": "一時停止", "input.placeholder": "ここにメッセージを入力...", "input.send": "送信", "input.settings": "設定", "input.topics": " トピック ", "input.translate": "{{target_language}}に翻訳", "input.upload": "画像またはドキュメントをアップロード", "input.upload.document": "ドキュメントをアップロード（モデルは画像をサポートしません）", "input.web_search": "ウェブ検索を有効にする", "input.web_search.button.ok": "設定に移動", "input.web_search.enable": "ウェブ検索を有効にする", "input.web_search.enable_content": "ウェブ検索の接続性を先に設定で確認する必要があります", "message.new.branch": "新しいブランチ", "message.new.branch.created": "新しいブランチが作成されました", "message.new.context": "新しいコンテキスト", "message.quote": "引用", "message.regenerate.model": "モデルを切り替え", "message.useful": "役立つ", "navigation": {"first": "最初のメッセージです", "history": "チャット履歴", "last": "最後のメッセージです", "next": "次のメッセージ", "prev": "前のメッセージ", "top": "トップに戻る", "bottom": "下部に戻る", "close": "閉じる"}, "resend": "再送信", "save": "保存", "settings.code_collapsible": "コードブロック折り畳み", "settings.code_wrappable": "コードブロック折り返し", "settings.code_cacheable": "コードブロックキャッシュ", "settings.code_cacheable.tip": "コードブロックのキャッシュは長いコードブロックのレンダリング時間を短縮できますが、メモリ使用量が増加します", "settings.code_cache_max_size": "キャッシュ上限", "settings.code_cache_max_size.tip": "キャッシュできる文字数の上限（千字符）。ハイライトされたコードの長さは純粋なテキストよりもはるかに長くなります。", "settings.code_cache_ttl": "キャッシュ期限", "settings.code_cache_ttl.tip": "キャッシュの有効期限（分単位）。", "settings.code_cache_threshold": "キャッシュ閾値", "settings.code_cache_threshold.tip": "キャッシュできる最小のコード長（千字符）。キャッシュできる最小のコード長を超えたコードブロックのみがキャッシュされます。", "settings.context_count": "コンテキスト", "settings.context_count.tip": "コンテキストに保持する以前のメッセージの数", "settings.max": "最大", "settings.max_tokens": "最大トークン制限を有効にする", "settings.max_tokens.confirm": "最大トークン制限を有効にする", "settings.max_tokens.confirm_content": "最大トークン制限を有効にすると、モデルが生成できる最大トークン数が制限されます。これにより、返される結果の長さに影響が出る可能性があります。モデルのコンテキスト制限に基づいて設定する必要があります。そうしないとエラーが発生します", "settings.max_tokens.tip": "モデルが生成できる最大トークン数。モデルのコンテキスト制限に基づいて設定する必要があります。そうしないとエラーが発生します", "settings.reset": "リセット", "settings.set_as_default": "デフォルトのアシスタントに適用", "settings.show_line_numbers": "コードに行番号を表示", "settings.temperature": "温度", "settings.temperature.tip": "低い値はモデルをより創造的で予測不可能にし、高い値はより決定論的で正確にします", "settings.thought_auto_collapse": "思考内容を自動的に折りたたむ", "settings.thought_auto_collapse.tip": "思考が終了したら思考内容を自動的に折りたたみます", "settings.top_p": "Top-P", "settings.top_p.tip": "デフォルト値は1で、値が小さいほど回答の多様性が減り、理解しやすくなります。値が大きいほど、AIの語彙範囲が広がり、多様性が増します", "suggestions.title": "提案された質問", "thinking": "思考中...", "topics.auto_rename": "自動リネーム", "topics.clear.title": "メッセージをクリア", "topics.copy.image": "画像としてコピー", "topics.copy.md": "Markdownとしてコピー", "topics.copy.title": "コピー", "topics.delete.shortcut": "{{key}}キーを押しながらで直接削除", "topics.edit.placeholder": "新しい名前を入力", "topics.edit.title": "名前を編集", "topics.export.image": "画像としてエクスポート", "topics.export.joplin": "<PERSON><PERSON><PERSON> にエクスポート", "topics.export.md": "Markdownとしてエクスポート", "topics.export.md.reason": "Markdown としてエクスポート (思考内容を含む)", "topics.export.notion": "Notion にエクスポート", "topics.export.obsidian": "Obsidian にエクスポート", "topics.export.obsidian_vault": "保管庫", "topics.export.obsidian_vault_placeholder": "保管庫名を選択してください", "topics.export.obsidian_path": "パス", "topics.export.obsidian_path_placeholder": "パスを選択してください", "topics.export.obsidian_atributes": "ノートの属性を設定", "topics.export.obsidian_btn": "確定", "topics.export.obsidian_created": "作成日時", "topics.export.obsidian_created_placeholder": "作成日時を選択してください", "topics.export.obsidian_export_failed": "エクスポート失敗", "topics.export.obsidian_export_success": "エクスポート成功", "topics.export.obsidian_operate": "処理方法", "topics.export.obsidian_operate_append": "追加", "topics.export.obsidian_operate_new_or_overwrite": "新規作成（既に存在する場合は上書き）", "topics.export.obsidian_operate_placeholder": "処理方法を選択してください", "topics.export.obsidian_operate_prepend": "先頭に追加", "topics.export.obsidian_source": "ソース", "topics.export.obsidian_source_placeholder": "ソースを入力してください", "topics.export.obsidian_tags": "タグ", "topics.export.obsidian_tags_placeholder": "タグを入力してください。複数のタグは英語のコンマで区切ってください", "topics.export.obsidian_title": "タイトル", "topics.export.obsidian_title_placeholder": "タイトルを入力してください", "topics.export.obsidian_title_required": "タイトルは空白にできません", "topics.export.obsidian_no_vaults": "Obsidianの保管庫が見つかりません", "topics.export.obsidian_loading": "読み込み中...", "topics.export.obsidian_fetch_error": "Obsidianの保管庫の取得に失敗しました", "topics.export.obsidian_fetch_folders_error": "フォルダ構造の取得に失敗しました", "topics.export.obsidian_no_vault_selected": "保管庫を選択してください", "topics.export.obsidian_select_vault_first": "最初に保管庫を選択してください", "topics.export.obsidian_root_directory": "ルートディレクトリ", "topics.export.title": "エクスポート", "topics.export.word": "Wordとしてエクスポート", "topics.export.yuque": "語雀にエクスポート", "topics.list": "トピックリスト", "topics.move_to": "移動先", "topics.new": "新しいトピック", "topics.pinned": "トピックを固定", "topics.prompt": "トピック提示語", "topics.prompt.edit.title": "トピック提示語を編集する", "topics.prompt.tips": "トピック提示語：現在のトピックに対して追加の補足提示語を提供", "topics.title": "トピック", "topics.unpinned": "固定解除", "translate": "翻訳", "topics.export.siyuan": "思源笔记にエクスポート", "topics.export.wait_for_title_naming": "タイトルを生成中...", "topics.export.title_naming_success": "タイトルの生成に成功しました", "topics.export.title_naming_failed": "タイトルの生成に失敗しました。デフォルトのタイトルを使用します", "input.translating": "翻訳中...", "input.upload.upload_from_local": "ローカルファイルをアップロード..."}, "code_block": {"collapse": "折りたたむ", "disable_wrap": "改行解除", "enable_wrap": "改行", "expand": "展開する"}, "common": {"add": "追加", "advanced_settings": "詳細設定", "and": "と", "assistant": "アシスタント", "avatar": "アバター", "back": "戻る", "cancel": "キャンセル", "chat": "チャット", "clear": "クリア", "close": "閉じる", "confirm": "確認", "copied": "コピーされました", "copy": "コピー", "cut": "切り取り", "default": "デフォルト", "delete": "削除", "description": "説明", "docs": "ドキュメント", "download": "ダウンロード", "duplicate": "複製", "edit": "編集", "expand": "展開", "collapse": "折りたたむ", "footnote": "引用内容", "footnotes": "脚注", "fullscreen": "全画面モードに入りました。F11キーで終了します", "knowledge_base": "ナレッジベース", "language": "言語", "model": "モデル", "models": "モデル", "more": "もっと", "name": "名前", "paste": "貼り付け", "prompt": "プロンプト", "provider": "プロバイダー", "regenerate": "再生成", "rename": "名前を変更", "reset": "リセット", "save": "保存", "search": "検索", "select": "選択", "topics": "トピック", "warning": "警告", "you": "あなた", "reasoning_content": "深く考察済み", "sort": {"pinyin": "ピンインでソート", "pinyin.asc": "ピンインで昇順ソート", "pinyin.desc": "ピンインで降順ソート"}}, "docs": {"title": "ドキュメント"}, "error": {"backup.file_format": "バックアップファイルの形式エラー", "chat.response": "エラーが発生しました。APIキーが設定されていない場合は、設定 > プロバイダーでキーを設定してください", "http": {"400": "リクエストに失敗しました。リクエストパラメータが正しいか確認してください。モデルの設定を変更した場合は、デフォルトの設定にリセットしてください", "401": "認証に失敗しました。APIキーが正しいか確認してください", "403": "アクセスが拒否されました。アカウントが実名認証されているか確認してください。またはサービスプロバイダーに問い合わせてください", "404": "モデルが見つからないか、リクエストパスが間違っています", "429": "リクエストが多すぎます。後でもう一度試してください", "500": "サーバーエラーが発生しました。後でもう一度試してください", "502": "ゲートウェイエラーが発生しました。後でもう一度試してください", "503": "サービスが利用できません。後でもう一度試してください", "504": "ゲートウェイタイムアウトが発生しました。後でもう一度試してください"}, "model.exists": "モデルが既に存在します", "no_api_key": "APIキーが設定されていません", "provider_disabled": "モデルプロバイダーが有効になっていません", "render": {"description": "メッセージの内容のレンダリングに失敗しました。メッセージの内容の形式が正しいか確認してください", "title": "レンダリングエラー"}, "user_message_not_found": "元のユーザーメッセージを見つけることができませんでした", "unknown": "不明なエラー"}, "export": {"assistant": "アシスタント", "attached_files": "添付ファイル", "conversation_details": "会話の詳細", "conversation_history": "会話履歴", "created": "作成日", "last_updated": "最終更新日", "messages": "メッセージ", "user": "ユーザー"}, "files": {"actions": "操作", "all": "すべてのファイル", "count": "ファイル", "created_at": "作成日", "delete": "削除", "delete.content": "ファイルを削除すると、ファイルがすべてのメッセージで参照されることを削除します。このファイルを削除してもよろしいですか？", "delete.paintings.warning": "画像に含まれているため、削除できません", "delete.title": "ファイルを削除", "document": "ドキュメント", "edit": "編集", "file": "ファイル", "image": "画像", "name": "名前", "open": "開く", "size": "サイズ", "text": "テキスト", "title": "ファイル", "type": "タイプ"}, "gpustack": {"keep_alive_time.description": "モデルがメモリに保持される時間（デフォルト：5分）", "keep_alive_time.placeholder": "分", "keep_alive_time.title": "保持時間", "title": "GPUStack"}, "history": {"continue_chat": "チャットを続ける", "locate.message": "メッセージを探す", "search.messages": "すべてのメッセージを検索", "search.placeholder": "トピックまたはメッセージを検索...", "search.topics.empty": "トピックが見つかりませんでした。Enterキーを押してすべてのメッセージを検索", "title": "トピック検索"}, "knowledge": {"add": {"title": "ナレッジベースを追加"}, "add_directory": "ディレクトリを追加", "add_file": "ファイルを追加", "add_note": "ノートを追加", "add_sitemap": "サイトマップを追加", "add_url": "URLを追加", "cancel_index": "インデックスをキャンセル", "chunk_overlap": "チャンクの重なり", "chunk_overlap_placeholder": "デフォルト（変更しないでください）", "chunk_overlap_tooltip": "隣接するチャンク間の重複内容量。チャンク間のコンテキスト関連性を確保し、長文テキストの処理効果を向上させます。", "chunk_size": "チャンクサイズ", "chunk_size_change_warning": "チャンクサイズと重複サイズの変更は、新しく追加された内容にのみ適用されます", "chunk_size_placeholder": "デフォルト（変更しないでください）", "chunk_size_too_large": "チャンクサイズはモデルのコンテキスト制限を超えることはできません（{{max_context}}）", "chunk_size_tooltip": "ドキュメントを分割し、各チャンクのサイズ。モデルのコンテキスト制限を超えないようにしてください。", "clear_selection": "選択をクリア", "delete": "削除", "delete_confirm": "このナレッジベースを削除してもよろしいですか？", "directories": "ディレクトリ", "directory_placeholder": "ディレクトリパスを入力", "document_count": "要求されたドキュメント分段数", "document_count_default": "デフォルト", "document_count_help": "要求されたドキュメント分段数が多いほど、付随する情報が多くなりますが、トークンの消費量も増加します", "drag_file": "ファイルをここにドラッグ", "edit_remark": "備考を編集", "edit_remark_placeholder": "備考内容を入力してください", "empty": "ナレッジベースが見つかりません", "file_hint": "{{file_types}} 形式をサポート", "index_all": "すべてをインデックス", "index_cancelled": "インデックスがキャンセルされました", "index_started": "インデックスを開始", "invalid_url": "無効なURL", "model_info": "モデル情報", "no_bases": "ナレッジベースがありません", "no_match": "知識ベースの内容が見つかりませんでした。", "no_provider": "ナレッジベースモデルプロバイダーが設定されていません。ナレッジベースはもうサポートされていません。新しいナレッジベースを作成してください", "not_set": "未設定", "not_support": "ナレッジベースデータベースエンジンが更新されました。このナレッジベースはもうサポートされていません。新しいナレッジベースを作成してください", "notes": "ノート", "notes_placeholder": "このナレッジベースの追加情報やコンテキストを入力...", "rename": "名前を変更", "search": "ナレッジベースを検索", "search_placeholder": "検索するテキストを入力", "settings": "ナレッジベース設定", "sitemap_placeholder": "サイトマップURLを入力", "sitemaps": "サイトマップ", "source": "ソース", "status": "状態", "status_completed": "完了", "status_failed": "失敗", "status_new": "追加済み", "status_pending": "保留中", "status_processing": "処理中", "threshold": "マッチング度閾値", "threshold_placeholder": "未設置", "threshold_too_large_or_small": "しきい値は0より大きく1より小さい必要があります", "threshold_tooltip": "ユーザーの質問と知識ベースの内容の関連性を評価するためのしきい値（0-1）", "title": "ナレッジベース", "topN": "返却される結果の数", "topN__too_large_or_small": "結果の数は100より大きくてはならず、1より小さくてはなりません。", "topN_placeholder": "未設定", "topN_tooltip": "返されるマッチ結果の数は、数値が大きいほどマッチ結果が多くなりますが、消費されるトークンも増えます。", "url_added": "URLが追加されました", "url_placeholder": "URLを入力, 複数のURLはEnterで区切る", "urls": "URL", "dimensions": "埋め込み次元", "dimensions_size_tooltip": "埋め込み次元のサイズは、数値が大きいほど埋め込み次元も大きくなりますが、消費するトークンも増えます。", "dimensions_size_placeholder": "デフォルト値（変更はお勧めしません）", "dimensions_size_too_large": "埋め込み次元はモデルのコンテキスト制限（{{max_context}}）を超えてはなりません。"}, "languages": {"arabic": "アラビア語", "chinese": "中国語", "chinese-traditional": "繁体字中国語", "english": "英語", "french": "フランス語", "german": "ドイツ語", "italian": "イタリア語", "japanese": "日本語", "korean": "韓国語", "portuguese": "ポルトガル語", "russian": "ロシア語", "spanish": "スペイン語"}, "lmstudio": {"keep_alive_time.description": "モデルがメモリに保持される時間（デフォルト：5分）", "keep_alive_time.placeholder": "分", "keep_alive_time.title": "保持時間", "title": "LM Studio"}, "mermaid": {"download": {"png": "PNGをダウンロード", "svg": "SVGをダウンロード"}, "resize": {"zoom-in": "拡大する", "zoom-out": "ズームアウト"}, "tabs": {"preview": "プレビュー", "source": "ソース"}, "title": "Mermaid図"}, "message": {"api.check.model.title": "検出に使用するモデルを選択してください", "api.connection.failed": "接続に失敗しました", "api.connection.success": "接続に成功しました", "assistant.added.content": "アシスタントが追加されました", "attachments": {"pasted_image": "クリップボード画像", "pasted_text": "クリップボードファイル"}, "backup.failed": "バックアップに失敗しました", "backup.start.success": "バックアップを開始しました", "backup.success": "バックアップに成功しました", "chat.completion.paused": "チャットの完了が一時停止されました", "citations": "参考文献", "copied": "コピーしました！", "copy.failed": "コピーに失敗しました", "copy.success": "コピーしました！", "error.chunk_overlap_too_large": "チャンクの重なりは、チャンクサイズを超えることはできません", "error.dimension_too_large": "内容のサイズが大きすぎます", "error.enter.api.host": "APIホストを入力してください", "error.enter.api.key": "APIキーを入力してください", "error.enter.model": "モデルを選択してください", "error.enter.name": "ナレッジベース名を入力してください", "error.get_embedding_dimensions": "埋込み次元を取得できませんでした", "error.invalid.api.host": "無効なAPIアドレスです", "error.invalid.api.key": "無効なAPIキーです", "error.invalid.enter.model": "モデルを選択してください", "error.invalid.proxy.url": "無効なプロキシURL", "error.invalid.webdav": "無効なWebDAV設定", "error.joplin.export": "Jo<PERSON>lin へのエクスポートに失敗しました。Jo<PERSON>lin が実行中であることを確認してください", "error.joplin.no_config": "Jo<PERSON>lin 認証トークン または URL が設定されていません", "error.invalid.nutstore": "無効なNutstore設定です", "error.invalid.nutstore_token": "無効なNutstoreトークンです", "error.markdown.export.preconf": "Markdown ファイルを事前設定されたパスにエクスポートできませんでした", "error.markdown.export.specified": "Markdown ファイルのエクスポートに失敗しました", "error.notion.export": "Notionへのエクスポートに失敗しました。接続状態と設定を確認してください", "error.notion.no_api_key": "Notion ApiKey または Notion DatabaseID が設定されていません", "error.yuque.export": "語雀へのエクスポートに失敗しました。接続状態と設定を確認してください", "group.delete.content": "分組メッセージを削除するとユーザーの質問と助け手の回答がすべて削除されます", "group.delete.title": "分組メッセージを削除", "ignore.knowledge.base": "インターネットモードが有効になっています。ナレッジベースを無視します", "info.notion.block_reach_limit": "会話が長すぎます。Notionにページごとにエクスポートしています", "loading.notion.exporting_progress": "Notionにエクスポート中 ({{current}}/{{total}})...", "loading.notion.preparing": "Notionへのエクスポートを準備中...", "mention.title": "モデルを切り替える", "message.code_style": "コードスタイル", "message.delete.content": "このメッセージを削除してもよろしいですか？", "message.delete.title": "メッセージを削除", "message.multi_model_style": "複数モデル回答スタイル", "message.multi_model_style.fold": "タブ表示", "message.multi_model_style.fold.compress": "緊湊配置に切り替える", "message.multi_model_style.fold.expand": "展開配置に切り替える", "message.multi_model_style.grid": "カード表示", "message.multi_model_style.horizontal": "横並び", "message.multi_model_style.vertical": "縦積み", "message.style": "メッセージスタイル", "message.style.bubble": "バブル", "message.style.plain": "プレーン", "regenerate.confirm": "再生成すると現在のメッセージが置き換えられます", "reset.confirm.content": "すべてのデータをリセットしてもよろしいですか？", "reset.double.confirm.content": "すべてのデータが失われます。続行しますか？", "reset.double.confirm.title": "データが失われます！！！", "restore.failed": "復元に失敗しました", "restore.success": "復元に成功しました", "save.success.title": "保存に成功しました", "searching": "検索中...", "success.joplin.export": "<PERSON><PERSON><PERSON> へのエクスポートに成功しました", "success.markdown.export.preconf": "Markdown ファイルを事前設定されたパスに正常にエクスポートしました", "success.markdown.export.specified": "Markdown ファイルを正常にエクスポートしました", "success.notion.export": "Notionへのエクスポートに成功しました", "success.yuque.export": "語雀へのエクスポートに成功しました", "switch.disabled": "現在の応答が完了するまで切り替えを無効にします", "tools": {"completed": "完了", "invoking": "呼び出し中", "error": "エラーが発生しました", "raw": "生データ", "preview": "プレビュー"}, "topic.added": "新しいトピックが追加されました", "upgrade.success.button": "再起動", "upgrade.success.content": "アップグレードを完了するためにアプリケーションを再起動してください", "upgrade.success.title": "アップグレードに成功しました", "warn.notion.exporting": "Notionにエクスポート中です。重複してエクスポートしないでください! ", "warning.rate.limit": "送信が頻繁すぎます。{{seconds}} 秒待ってから再試行してください。", "error.siyuan.export": "思源ノートのエクスポートに失敗しました。接続状態を確認し、ドキュメントに従って設定を確認してください", "error.siyuan.no_config": "思源ノートのAPIアドレスまたはトークンが設定されていません", "success.siyuan.export": "思源ノートへのエクスポートに成功しました", "warn.yuque.exporting": "語雀にエクスポート中です。重複してエクスポートしないでください!", "warn.siyuan.exporting": "思源ノートにエクスポート中です。重複してエクスポートしないでください!", "error.yuque.no_config": "語雀のAPIアドレスまたはトークンが設定されていません"}, "minapp": {"popup": {"refresh": "更新", "close": "ミニアプリを閉じる", "minimize": "ミニアプリを最小化", "devtools": "開発者ツール", "openExternal": "ブラウザで開く", "rightclick_copyurl": "右クリックでURLをコピー", "open_link_external_on": "現在：ブラウザで開く", "open_link_external_off": "現在：デフォルトのウィンドウで開く"}, "sidebar.add.title": "サイドバーに追加", "sidebar.remove.title": "サイドバーから削除", "sidebar.close.title": "閉じる", "sidebar.closeall.title": "すべて閉じる", "sidebar.hide.title": "ミニアプリを隠す", "title": "ミニアプリ"}, "miniwindow": {"clipboard": {"empty": "クリップボードが空です"}, "feature": {"chat": "この質問に回答", "explanation": "説明", "summary": "内容要約", "translate": "テキスト翻訳"}, "footer": {"copy_last_message": "C キーを押してコピー", "esc": "ESC キーを押して{{action}}", "esc_back": "戻る", "esc_close": "ウィンドウを閉じる", "backspace_clear": "バックスペースを押してクリアします"}, "input": {"placeholder": {"empty": "{{model}} に質問してください...", "title": "下のテキストに対して何をしますか？"}}, "tooltip": {"pin": "上部ウィンドウ"}}, "models": {"add_parameter": "パラメータを追加", "all": "すべて", "custom_parameters": "カスタムパラメータ", "dimensions": "{{dimensions}} 次元", "edit": "モデルを編集", "embedding": "埋め込み", "embedding_model": "埋め込み模型", "embedding_model_tooltip": "設定->モデルサービス->管理で追加", "function_calling": "関数呼び出し", "no_matches": "利用可能なモデルがありません", "parameter_name": "パラメータ名", "parameter_type": {"boolean": "真偽値", "json": "JSON", "number": "数値", "string": "テキスト"}, "pinned": "固定済み", "rerank_model": "再順序付けモデル", "rerank_model_support_provider": "現在の再順序付けモデルは、{{provider}} のみサポートしています", "rerank_model_tooltip": "設定->モデルサービスに移動し、管理ボタンをクリックして追加します。", "search": "モデルを検索...", "stream_output": "ストリーム出力", "type": {"embedding": "埋め込み", "free": "無料", "function_calling": "ツール", "reasoning": "推論", "rerank": "再順序付け", "select": "モデルタイプを選択", "text": "テキスト", "vision": "画像", "websearch": "ウェブ検索"}}, "navbar": {"expand": "ダイアログを展開", "hide_sidebar": "サイドバーを非表示", "show_sidebar": "サイドバーを表示"}, "ollama": {"keep_alive_time.description": "モデルがメモリに保持される時間（デフォルト：5分）", "keep_alive_time.placeholder": "分", "keep_alive_time.title": "保持時間", "title": "Ollama"}, "paintings": {"button.delete.image": "画像を削除", "button.delete.image.confirm": "この画像を削除してもよろしいですか？", "button.new.image": "新しい画像", "guidance_scale": "ガイダンススケール", "guidance_scale_tip": "分類器なしのガイダンス。モデルが関連する画像を探す際にプロンプトにどれだけ従うかを制御します", "image.size": "画像サイズ", "inference_steps": "推論ステップ数", "inference_steps_tip": "実行する推論ステップ数。ステップ数が多いほど品質が向上しますが、時間がかかります", "negative_prompt": "ネガティブプロンプト", "negative_prompt_tip": "画像に含めたくない内容を説明します", "number_images": "生成数", "number_images_tip": "生成する画像の数（1-4）", "prompt_enhancement": "プロンプト強化", "prompt_enhancement_tip": "オンにすると、プロンプトを詳細でモデルに適したバージョンに書き直します", "prompt_placeholder": "作成したい画像を説明します。例：夕日の湖畔、遠くに山々", "regenerate.confirm": "これにより、既存の生成画像が置き換えられます。続行しますか？", "seed": "シード", "seed_tip": "同じシードとプロンプトで似た画像を生成できます", "title": "画像"}, "plantuml": {"download": {"failed": "ダウンロードに失敗しました。ネットワークを確認してください", "png": "PNG をダウンロード", "svg": "SVG をダウンロード"}, "tabs": {"preview": "プレビュー", "source": "ソースコード"}, "title": "PlantUML 図表"}, "prompts": {"explanation": "この概念を説明してください", "summarize": "このテキストを要約してください", "title": "あなたは会話を得意とするアシスタントです。ユーザーの会話を10文字以内のタイトルに要約し、ユーザーの主言語と一致していることを確認してください。句読点や特殊記号は使用しないでください。"}, "provider": {"aihubmix": "AiHubMix", "alayanew": "Alaya NeW", "anthropic": "Anthropic", "azure-openai": "Azure OpenAI", "baichuan": "百川", "baidu-cloud": "Baidu Cloud", "copilot": "GitHub Copilot", "dashscope": "Alibaba Cloud", "deepseek": "DeepSeek", "dmxapi": "DMXAPI", "doubao": "Volcengine", "fireworks": "Fireworks", "gemini": "Gemini", "gitee-ai": "Gitee AI", "github": "GitHub Models", "gpustack": "GPUStack", "grok": "Grok", "groq": "Groq", "hunyuan": "腾讯混元", "hyperbolic": "Hyperbolic", "infini": "Infini", "jina": "<PERSON><PERSON>", "lmstudio": "LM Studio", "minimax": "MiniMax", "mistral": "<PERSON><PERSON><PERSON>", "modelscope": "ModelScope", "moonshot": "月の暗面", "nvidia": "NVIDIA", "o3": "O3", "ocoolai": "ocoolAI", "ollama": "Ollama", "openai": "OpenAI", "openrouter": "OpenRouter", "perplexity": "Perplexity", "ppio": "PPIO パイオウクラウド", "qwenlm": "QwenLM", "silicon": "SiliconFlow", "stepfun": "<PERSON><PERSON><PERSON>", "tencent-cloud-ti": "Tencent Cloud TI", "together": "Together", "xirang": "天翼クラウド 息壤", "yi": "零一万物", "zhinao": "360智脳", "zhipu": "智譜AI", "voyageai": "Voyage AI", "qiniu": "七牛云"}, "restore": {"confirm": "データを復元しますか？", "confirm.button": "バックアップファイルを選択", "content": "復元操作は現在のアプリデータをバックアップデータで上書きします。復元処理には時間がかかる場合があります。", "progress": {"completed": "復元完了", "copying_files": "ファイルコピー中... {{progress}}%", "extracting": "バックアップ解凍中...", "preparing": "復元準備中...", "reading_data": "データ読み込み中...", "title": "復元進捗"}, "title": "データ復元"}, "settings": {"about": "について", "about.checkingUpdate": "更新を確認中...", "about.checkUpdate": "更新を確認", "about.checkUpdate.available": "今すぐ更新", "about.contact.button": "メール", "about.contact.title": "連絡先", "about.description": "クリエイターのための強力なAIアシスタント", "about.downloading": "ダウンロード中...", "about.feedback.button": "フィードバック", "about.feedback.title": "フィードバック", "about.license.button": "ライセンス", "about.license.title": "ライセンス", "about.releases.button": "リリース", "about.releases.title": "リリースノート", "about.social.title": "ソーシャルアカウント", "about.title": "について", "about.updateAvailable": "新しいバージョン {{version}} が見つかりました", "about.updateError": "更新エラー", "about.updateNotAvailable": "最新バージョンを使用しています", "about.website.button": "ウェブサイト", "about.website.title": "公式ウェブサイト", "advanced.auto_switch_to_topics": "トピックに自動的に切り替える", "advanced.title": "詳細設定", "assistant": "デフォルトアシスタント", "assistant.model_params": "モデルパラメータ", "assistant.icon.type": "モデルアイコンタイプ", "assistant.icon.type.model": "モデルアイコン", "assistant.icon.type.emoji": "Emoji アイコン", "assistant.icon.type.none": "表示しない", "assistant.title": "デフォルトアシスタント", "data": {"app_data": "アプリデータ", "app_knowledge": "ナレッジベースファイル", "app_knowledge.button.delete": "ファイルを削除", "app_knowledge.remove_all": "ナレッジベースファイルを削除", "app_knowledge.remove_all_confirm": "ナレッジベースファイルを削除すると、ナレッジベース自体は削除されません。これにより、ストレージ容量を節約できます。続行しますか？", "app_knowledge.remove_all_success": "ファイル削除成功", "app_logs": "アプリログ", "clear_cache": {"button": "キャッシュをクリア", "confirm": "キャッシュをクリアすると、アプリのキャッシュデータ（ミニアプリデータを含む）が削除されます。この操作は元に戻せません。続行しますか？", "error": "キャッシュのクリアに失敗しました", "success": "キャッシュがクリアされました", "title": "キャッシュをクリア"}, "data.title": "データディレクトリ", "divider.basic": "基本データ設定", "divider.cloud_storage": "クラウドバックアップ設定", "divider.export_settings": "エクスポート設定", "divider.third_party": "サードパーティー連携", "hour_interval_one": "{{count}} 時間", "hour_interval_other": "{{count}} 時間", "export_menu": {"title": "エクスポートメニュー設定", "image": "画像としてエクスポート", "markdown": "Markdownとしてエクスポート", "markdown_reason": "Markdownとしてエクスポート（思考内容を含む）", "notion": "Notionにエクスポート", "yuque": "語雀にエクスポート", "obsidian": "Obsidianにエクスポート", "siyuan": "思源ノートにエクスポート", "joplin": "Joplinにエクスポート", "docx": "Wordとしてエクスポート"}, "joplin": {"check": {"button": "確認", "empty_token": "Jo<PERSON>lin 認証トークン を先に入力してください", "empty_url": "Joplin 剪輯服務 URL を先に入力してください", "fail": "Jo<PERSON>lin 接続確認に失敗しました", "success": "Jo<PERSON>lin 接続確認に成功しました"}, "help": "Joplin オプションで、剪輯サービスを有効にしてください。ポート番号を確認し、認証トークンをコピーしてください", "title": "<PERSON><PERSON><PERSON> 設定", "token": "Jo<PERSON>lin 認証トークン", "token_placeholder": "Jo<PERSON>lin 認証トークンを入力してください", "url": "Joplin 剪輯服務 URL", "url_placeholder": "http://127.0.0.1:41184/"}, "markdown_export.force_dollar_math.help": "有効にすると、Markdownにエクスポートする際にLaTeX数式を$$で強制的にマークします。注意：この設定はNotion、Yuqueなど、Markdownを通じたすべてのエクスポート方法にも影響します。", "markdown_export.force_dollar_math.title": "LaTeX数式に$$を強制使用", "markdown_export.help": "入力された場合、エクスポート時に自動的にこのパスに保存されます。未入力の場合、保存ダイアログが表示されます。", "markdown_export.path": "デフォルトのエクスポートパス", "markdown_export.path_placeholder": "エクスポートパス", "markdown_export.select": "選択", "markdown_export.title": "Markdown エクスポート", "minute_interval_one": "{{count}} 分", "minute_interval_other": "{{count}} 分", "notion.api_key": "Notion APIキー", "notion.api_key_placeholder": "Notion APIキーを入力してください", "notion.auto_split": "ダイアログをエクスポートすると自動ページ分割", "notion.auto_split_tip": "ダイアログが長い場合、Notionに自動的にページ分割してエクスポートします", "notion.check": {"button": "確認", "empty_api_key": "Api_keyが設定されていません", "empty_database_id": "Database_idが設定されていません", "error": "接続エラー、ネットワーク設定とApi_keyとDatabase_idを確認してください", "fail": "接続エラー、ネットワーク設定とApi_keyとDatabase_idを確認してください", "success": "接続に成功しました。"}, "notion.database_id": "Notion データベースID", "notion.database_id_placeholder": "Notion データベースIDを入力してください", "notion.help": "Notion 設定ドキュメント", "notion.page_name_key": "ページタイトルフィールド名", "notion.page_name_key_placeholder": "ページタイトルフィールド名を入力してください。デフォルトは Name です", "notion.split_size": "自動ページ分割サイズ", "notion.split_size_help": "Notion無料版ユーザーは90、有料版ユーザーは24990、デフォルトは90", "notion.split_size_placeholder": "ページごとのブロック数制限を入力してください(デフォルト90)", "notion.title": "Notion 設定", "title": "データ設定", "webdav": {"autoSync": "自動バックアップ", "autoSync.off": "オフ", "backup.button": "WebDAVにバックアップ", "backup.modal.filename.placeholder": "バックアップファイル名を入力してください", "backup.modal.title": "WebDAV にバックアップ", "backup.manager.title": "バックアップデータ管理", "backup.manager.refresh": "更新", "backup.manager.delete.selected": "選択したものを ", "backup.manager.delete.text": "削除", "backup.manager.restore.text": "復元", "backup.manager.restore.success": "復元が成功しました、アプリケーションは間もなく更新されます", "backup.manager.restore.error": "復元に失敗しました", "backup.manager.delete.confirm.title": "削除の確認", "backup.manager.delete.confirm.single": "バックアップファイル \"{{fileName}}\" を削除してもよろしいですか？この操作は元に戻せません。", "backup.manager.delete.confirm.multiple": "選択した {{count}} 個のバックアップファイルを削除してもよろしいですか？この操作は元に戻せません。", "backup.manager.delete.success.single": "削除が成功しました", "backup.manager.delete.success.multiple": "{{count}} 個のバックアップファイルを削除しました", "backup.manager.delete.error": "削除に失敗しました", "backup.manager.fetch.error": "バックアップファイルの取得に失敗しました", "backup.manager.select.files.delete": "削除するバックアップファイルを選択してください", "backup.manager.columns.fileName": "ファイル名", "backup.manager.columns.modifiedTime": "更新日時", "backup.manager.columns.size": "サイズ", "backup.manager.columns.actions": "操作", "host": "WebDAVホスト", "host.placeholder": "http://localhost:8080", "hour_interval_one": "{{count}} 時間", "hour_interval_other": "{{count}} 時間", "lastSync": "最終バックアップ", "minute_interval_one": "{{count}} 分", "minute_interval_other": "{{count}} 分", "noSync": "次回のバックアップを待機中", "password": "WebDAVパスワード", "path": "WebDAVパス", "path.placeholder": "/backup", "restore.button": "WebDAVから復元", "restore.confirm.content": "WebDAV から復元すると現在のデータが上書きされます。続行しますか？", "restore.confirm.title": "復元を確認", "restore.content": "WebDAVから復元すると現在のデータが上書きされます。続行しますか？", "restore.modal.select.placeholder": "復元するバックアップファイルを選択してください", "restore.modal.title": "WebDAV から復元", "restore.title": "WebDAVから復元", "syncError": "バックアップエラー", "syncStatus": "バックアップ状態", "title": "WebDAV", "user": "WebDAVユーザー", "maxBackups": "最大バックアップ数", "maxBackups.unlimited": "無制限"}, "yuque": {"check": {"button": "接続確認", "empty_repo_url": "先にナレッジベースURLを入力してください", "empty_token": "先にYuqueトークンを入力してください", "fail": "Yuque接続確認に失敗しました", "success": "Yuque接続確認に成功しました"}, "help": "Yuqueトークンを取得", "repo_url": "ナレッジベースURL", "repo_url_placeholder": "https://www.yuque.com/username/xxx", "title": "Yuque設定", "token": "Yuqueトークン", "token_placeholder": "Yuqueトークンを入力してください"}, "obsidian": {"title": "Obsidian 設定", "default_vault": "デフォルトの Obsidian 保管庫", "default_vault_placeholder": "デフォルトの Obsidian 保管庫を選択してください", "default_vault_loading": "Obsidian 保管庫を取得中...", "default_vault_no_vaults": "Obsidian 保管庫が見つかりません", "default_vault_fetch_error": "Obsidian 保管庫の取得に失敗しました", "default_vault_export_failed": "エクスポートに失敗しました"}, "siyuan": {"title": "思源ノート設定", "api_url": "APIアドレス", "api_url_placeholder": "例：http://127.0.0.1:6806", "token": "APIトークン", "token.help": "思源ノート->設定->について で取得", "token_placeholder": "思源ノートトークンを入力してください", "box_id": "ノートブックID", "box_id_placeholder": "ノートブックIDを入力してください", "root_path": "ドキュメントルートパス", "root_path_placeholder": "例：/CherryStudio", "check": {"title": "接続チェック", "button": "チェック", "empty_config": "APIアドレスとトークンを入力してください", "success": "接続成功", "fail": "接続失敗、APIアドレスとトークンを確認してください", "error": "接続エラー、ネットワーク接続を確認してください"}}, "nutstore": {"title": "Nutstore設定", "isLogin": "ログイン済み", "notLogin": "未ログイン", "login.button": "ログイン", "logout.button": "ログアウト", "logout.title": "Nutstoreからログアウトしますか？", "logout.content": "ログアウト後、Nutstoreへのバックアップや復元ができなくなります。", "checkConnection.name": "接続確認", "checkConnection.success": "Nutstoreに接続しました", "checkConnection.fail": "Nutstore接続に失敗しました", "username": "Nutstoreユーザー名", "path": "Nutstoreストレージパス", "path.placeholder": "Nutstoreストレージパスを入力", "backup.button": "Nutstoreにバックアップ", "restore.button": "Nutstoreから復元", "pathSelector.title": "Nutstoreストレージパス", "pathSelector.return": "戻る", "pathSelector.currentPath": "現在のパス", "new_folder.button.confirm": "確認", "new_folder.button.cancel": "キャンセル", "new_folder.button": "新しいフォルダー"}, "message_title.use_topic_naming.title": "トピック命名モデルを使用してメッセージのタイトルを作成", "message_title.use_topic_naming.help": "この設定は、すべてのMarkdownエクスポート方法に影響します。"}, "display.assistant.title": "アシスタント設定", "display.custom.css": "カスタムCSS", "display.custom.css.cherrycss": "cherrycss.comから取得", "display.custom.css.placeholder": "/* ここにカスタムCSSを入力 */", "display.sidebar.chat.hiddenMessage": "アシスタントは基本的な機能であり、非表示はサポートされていません", "display.sidebar.disabled": "アイコンを非表示", "display.sidebar.empty": "非表示にする機能を左側からここにドラッグ", "display.sidebar.files.icon": "ファイルのアイコンを表示", "display.sidebar.knowledge.icon": "ナレッジのアイコンを表示", "display.sidebar.minapp.icon": "ミニアプリのアイコンを表示", "display.sidebar.painting.icon": "絵画のアイコンを表示", "display.sidebar.title": "サイドバー設定", "display.sidebar.translate.icon": "翻訳のアイコンを表示", "display.sidebar.visible": "アイコンを表示", "display.title": "表示設定", "display.topic.title": "トピック設定", "miniapps": {"title": "ミニアプリ設定", "disabled": "非表示のミニアプリ", "empty": "非表示にするミニアプリを左側からここにドラッグしてください", "visible": "表示するミニアプリ", "open_link_external": {"title": "新視窗のリンクをブラウザで開く"}, "cache_settings": "キャッシュ設定", "cache_title": "ミニアプリのキャッシュ数", "cache_description": "メモリに保持するアクティブなミニアプリの最大数を設定します", "reset_tooltip": "デフォルト値にリセット", "display_title": "ミニアプリ表示設定", "sidebar_title": "サイドバーのアクティブなミニアプリ表示", "sidebar_description": "サイドバーにアクティブなミニアプリを表示するかどうかを設定します", "cache_change_notice": "設定値に達するまでミニアプリの開閉が行われた後に変更が適用されます"}, "font_size.title": "メッセージのフォントサイズ", "general": "一般設定", "general.avatar.reset": "アバターをリセット", "general.backup.button": "バックアップ", "general.backup.title": "データのバックアップと復元", "general.display.title": "表示設定", "general.emoji_picker": "絵文字ピッカー", "general.image_upload": "画像アップロード", "general.reset.button": "リセット", "general.reset.title": "データをリセット", "general.restore.button": "復元", "general.title": "一般設定", "general.user_name": "ユーザー名", "general.user_name.placeholder": "ユーザー名を入力", "general.view_webdav_settings": "WebDAV設定を表示", "input.auto_translate_with_space": "スペースを3回押して翻訳", "input.target_language": "目標言語", "input.target_language.chinese": "簡体字中国語", "input.target_language.chinese-traditional": "繁体字中国語", "input.target_language.english": "英語", "input.target_language.japanese": "日本語", "input.target_language.russian": "ロシア語", "launch.onboot": "起動時に自動で開始", "launch.title": "起動", "launch.totray": "起動時にトレイに最小化", "mcp": {"actions": "操作", "active": "有効", "addError": "サーバーの追加に失敗しました", "addServer": "サーバーを追加", "addSuccess": "サーバーが正常に追加されました", "args": "引数", "argsTooltip": "1行に1つの引数を入力してください", "baseUrlTooltip": "リモートURLアドレス", "command": "コマンド", "sse": "サーバー送信イベント (sse)", "streamableHttp": "ストリーミング可能なHTTP (streamable)", "stdio": "標準入力/出力 (stdio)", "inMemory": "メモリ", "config_description": "モデルコンテキストプロトコルサーバーの設定", "deleteError": "サーバーの削除に失敗しました", "deleteSuccess": "サーバーが正常に削除されました", "dependenciesInstall": "依存関係をインストール", "dependenciesInstalling": "依存関係をインストール中...", "description": "説明", "duplicateName": "同じ名前のサーバーが既に存在します", "editJson": "JSONを編集", "editServer": "サーバーを編集", "env": "環境変数", "envTooltip": "形式: KEY=value, 1行に1つ", "headers": "ヘッダー", "headersTooltip": "HTTP リクエストのカスタムヘッダー", "findMore": "MCP を見つける", "searchNpx": "MCP を検索", "install": "インストール", "installError": "依存関係のインストールに失敗しました", "installSuccess": "依存関係のインストールに成功しました", "jsonFormatError": "JSONフォーマットエラー", "jsonModeHint": "MCPサーバー設定のJSON表現を編集します。保存する前に、フォーマットが正しいことを確認してください。", "jsonSaveError": "JSON設定の保存に失敗しました", "jsonSaveSuccess": "JSON設定が保存されました。", "missingDependencies": "が不足しています。続行するにはインストールしてください。", "name": "名前", "noServers": "サーバーが設定されていません", "newServer": "MCP サーバー", "npx_list": {"actions": "アクション", "description": "説明", "no_packages": "パッケージが見つかりません", "npm": "NPM", "package_name": "パッケージ名", "scope_placeholder": "npm スコープを入力 (例: @your-org)", "scope_required": "npm スコープを入力してください", "search": "検索", "search_error": "パッケージの検索に失敗しました", "usage": "使用法", "version": "バージョン"}, "serverPlural": "サーバー", "serverSingular": "サーバー", "title": "MCP サーバー", "startError": "起動に失敗しました", "type": "タイプ", "updateError": "サーバーの更新に失敗しました", "updateSuccess": "サーバーが正常に更新されました", "url": "URL", "errors": {"32000": "MCP サーバーが起動しませんでした。パラメーターを確認してください"}, "editMcpJson": "MCP 設定を編集", "installHelp": "インストールヘルプを取得", "tabs": {"general": "一般", "description": "説明", "tools": "ツール", "prompts": "プロンプト", "resources": "リソース"}, "tools": {"inputSchema": "入力スキーマ", "availableTools": "利用可能なツール", "noToolsAvailable": "利用可能なツールなし", "loadError": "ツール取得エラー"}, "prompts": {"availablePrompts": "利用可能なプロンプト", "noPromptsAvailable": "利用可能なプロンプトはありません", "arguments": "引数", "requiredField": "必須フィールド", "genericError": "プロンプト取得エラー", "loadError": "プロンプト取得エラー"}, "resources": {"noResourcesAvailable": "利用可能なリソースはありません", "availableResources": "利用可能なリソース", "uri": "URI", "mimeType": "MIMEタイプ", "size": "サイズ", "blob": "バイナリデータ", "blobInvisible": "バイナリデータを非表示", "text": "テキスト"}, "deleteServer": "サーバーを削除", "deleteServerConfirm": "このサーバーを削除してもよろしいですか？", "registry": "パッケージ管理レジストリ", "registryTooltip": "デフォルトのレジストリでネットワークの問題が発生した場合、パッケージインストールに使用するレジストリを選択してください。", "registryDefault": "デフォルト", "not_support": "モデルはサポートされていません", "user": "ユーザー", "system": "システム", "types": {"inMemory": "組み込み", "sse": "SSE", "streamableHttp": "ストリーミング", "stdio": "STDIO"}, "sync": {"title": "サーバーの同期", "selectProvider": "プロバイダーを選択：", "discoverMcpServers": "MCPサーバーを発見", "discoverMcpServersDescription": "プラットフォームを訪れて利用可能なMCPサーバーを発見", "getToken": "API トークンを取得する", "getTokenDescription": "アカウントから個人用 API トークンを取得します", "setToken": "トークンを入力してください", "tokenRequired": "API トークンは必須です", "tokenPlaceholder": "ここに API トークンを入力してください", "button": "同期する", "error": "MCPサーバーの同期エラー", "success": "MCPサーバーの同期成功", "unauthorized": "同期が許可されていません", "noServersAvailable": "利用可能な MCP サーバーがありません"}}, "messages.divider": "メッセージ間に区切り線を表示", "messages.grid_columns": "メッセージグリッドの表示列数", "messages.grid_popover_trigger": "グリッド詳細トリガー", "messages.grid_popover_trigger.click": "クリックで表示", "messages.grid_popover_trigger.hover": "ホバーで表示", "messages.input.paste_long_text_as_file": "長いテキストをファイルとして貼り付け", "messages.input.paste_long_text_threshold": "長いテキストの長さ", "messages.input.send_shortcuts": "送信ショートカット", "messages.input.show_estimated_tokens": "推定トークン数を表示", "messages.input.title": "入力設定", "messages.input.enable_quick_triggers": "'/' と '@' を有効にしてクイックメニューを表示します。", "messages.input.enable_delete_model": "バックスペースキーでモデル/添付ファイルを削除します。", "messages.markdown_rendering_input_message": "Markdownで入力メッセージをレンダリング", "messages.math_engine": "数式エンジン", "messages.math_engine.none": "なし", "messages.metrics": "最初のトークンまでの時間 {{time_first_token_millsec}}ms | トークン速度 {{token_speed}} tok/sec", "messages.model.title": "モデル設定", "messages.navigation": "メッセージナビゲーション", "messages.navigation.anchor": "会話アンカー", "messages.navigation.buttons": "上下ボタン", "messages.navigation.none": "表示しない", "messages.title": "メッセージ設定", "messages.use_serif_font": "セリフフォントを使用", "model": "デフォルトモデル", "models.add.add_model": "モデルを追加", "models.add.group_name": "グループ名", "models.add.group_name.placeholder": "例：ChatGPT", "models.add.group_name.tooltip": "例：ChatGPT", "models.add.model_id": "モデルID", "models.add.model_id.placeholder": "必須 例：gpt-3.5-turbo", "models.add.model_id.tooltip": "例：gpt-3.5-turbo", "models.add.model_name": "モデル名", "models.add.model_name.placeholder": "例：GPT-3.5", "models.check.all": "すべて", "models.check.all_models_passed": "すべてのモデルチェックが成功しました", "models.check.button_caption": "健康チェック", "models.check.disabled": "閉じる", "models.check.enable_concurrent": "並行チェック", "models.check.enabled": "開く", "models.check.failed": "失敗", "models.check.keys_status_count": "合格：{{count_passed}}個のキー、不合格：{{count_failed}}個のキー", "models.check.model_status_summary": "{{provider}}: {{count_passed}} 個のモデルが健康チェックを完了しました（{{count_partial}} 個のモデルは一部のキーにアクセスできませんでした）、{{count_failed}} 個のモデルは完全にアクセスできませんでした。", "models.check.no_api_keys": "APIキーが見つかりません。まずAPIキーを追加してください。", "models.check.passed": "成功", "models.check.select_api_key": "使用するAPIキーを選択：", "models.check.single": "単一", "models.check.start": "開始", "models.check.title": "モデル健康チェック", "models.check.use_all_keys": "キー", "models.default_assistant_model": "デフォルトアシスタントモデル", "models.default_assistant_model_description": "新しいアシスタントを作成する際に使用されるモデル。アシスタントがモデルを設定していない場合、このモデルが使用されます", "models.empty": "モデルが見つかりません", "models.enable_topic_naming": "トピックの自動命名", "models.manage.add_whole_group": "グループ全体を追加", "models.manage.remove_whole_group": "グループ全体を削除", "models.topic_naming_model": "トピック命名モデル", "models.topic_naming_model_description": "新しいトピックを自動的に命名する際に使用されるモデル", "models.topic_naming_model_setting_title": "トピック命名モデルの設定", "models.topic_naming_prompt": "トピック命名プロンプト", "models.translate_model": "翻訳モデル", "models.translate_model_description": "翻訳サービスに使用されるモデル", "models.translate_model_prompt_message": "翻訳モデルのプロンプトを入力してください", "models.translate_model_prompt_title": "翻訳モデルのプロンプト", "moresetting": "詳細設定", "moresetting.check.confirm": "選択を確認", "moresetting.check.warn": "このオプションを選択する際は慎重に行ってください。誤った選択はモデルの誤動作を引き起こす可能性があります！", "moresetting.warn": "リスク警告", "provider": {"add.name": "プロバイダー名", "add.name.placeholder": "例：OpenAI", "add.title": "プロバイダーを追加", "add.type": "プロバイダータイプ", "api.url.preview": "プレビュー: {{url}}", "api.url.reset": "リセット", "api.url.tip": "/で終わる場合、v1を無視します。#で終わる場合、入力されたアドレスを強制的に使用します", "api_host": "APIホスト", "api_key": "APIキー", "api_key.tip": "複数のキーはカンマで区切ります", "api_version": "APIバージョン", "basic_auth": "HTTP 認証", "basic_auth.tip": "サーバー展開によるインスタンスに適用されます（ドキュメントを参照）。現在はBasicスキーム（RFC7617）のみをサポートしています。", "basic_auth.user_name": "ユーザー名", "basic_auth.user_name.tip": "空欄で無効化", "basic_auth.password": "パスワード", "basic_auth.password.tip": "", "charge": "残高充電", "bills": "費用帳單", "check": "チェック", "check_all_keys": "すべてのキーをチェック", "check_multiple_keys": "複数のAPIキーをチェック", "oauth": {"button": "{{provider}} アカウントでログイン", "description": "本サービスは<website>{{provider}}</website>によって提供されます", "official_website": "公式サイト"}, "copilot": {"auth_failed": "Github Copilotの認証に失敗しました。", "auth_success": "G<PERSON><PERSON> Copilotの認証が成功しました", "auth_success_title": "認証成功", "code_failed": "デバイスコードの取得に失敗しました。再試行してください。", "code_generated_desc": "デバイスコードを下記のブラウザリンクにコピーしてください。", "code_generated_title": "デバイスコードを取得する", "confirm_login": "過度使用すると、あなたのGithubアカウントが停止される可能性があるため、慎重に使用してください!!!!", "confirm_title": "リスク警告", "connect": "GitHubに接続する", "custom_headers": "カスタムリクエストヘッダー", "description": "あなたのGithubアカウントはCopilotを購読する必要があります。", "expand": "展開", "headers_description": "カスタムリクエストヘッダー（JSONフォーマット）", "invalid_json": "JSONフォーマットエラー", "login": "GitHubにログインする", "logout": "GitHubから退出する", "logout_failed": "ログアウトに失敗しました。もう一度お試しください。", "logout_success": "正常にログアウトしました。", "model_setting": "モデル設定", "open_verification_first": "上のリンクをクリックして、確認ページにアクセスしてください。", "rate_limit": "レート制限", "tooltip": "Github Copilot を使用するには、まず Github にログインする必要があります。"}, "delete.content": "このプロバイダーを削除してもよろしいですか？", "delete.title": "プロバイダーを削除", "docs_check": "チェック", "docs_more_details": "詳細を確認", "get_api_key": "APIキーを取得", "is_not_support_array_content": "互換モードを有効にする", "no_models_for_check": "チェックするモデルがありません（例：会話モデル）", "not_checked": "未チェック", "remove_duplicate_keys": "重複キーを削除", "remove_invalid_keys": "無効なキーを削除", "search": "プロバイダーを検索...", "search_placeholder": "モデルIDまたは名前を検索", "title": "モデルプロバイダー", "notes": {"title": "モデルノート", "placeholder": "Markdown形式の内容を入力してください...", "markdown_editor_default_value": "プレビュー領域"}}, "proxy": {"mode": {"custom": "カスタムプロキシ", "none": "プロキシを使用しない", "system": "システムプロキシ", "title": "プロキシモード"}, "title": "プロキシ設定"}, "proxy.title": "プロキシアドレス", "quickAssistant": {"click_tray_to_show": "トレイアイコンをクリックして起動", "enable_quick_assistant": "クイックアシスタントを有効にする", "read_clipboard_at_startup": "起動時にクリップボードを読み取る", "title": "クイックアシスタント", "use_shortcut_to_show": "トレイアイコンを右クリックするか、ショートカットキーで起動できます"}, "shortcuts": {"action": "操作", "clear_shortcut": "ショートカットをクリア", "clear_topic": "メッセージを消去", "copy_last_message": "最後のメッセージをコピー", "key": "キー", "mini_window": "クイックアシスタント", "new_topic": "新しいトピック", "press_shortcut": "ショートカットを押す", "reset_defaults": "デフォルトのショートカットをリセット", "reset_defaults_confirm": "すべてのショートカットをリセットしてもよろしいですか？", "reset_to_default": "デフォルトにリセット", "search_message": "メッセージを検索", "show_app": "アプリを表示/非表示", "show_settings": "設定を開く", "title": "ショートカット", "toggle_new_context": "コンテキストをクリア", "toggle_show_assistants": "アシスタントの表示を切り替え", "toggle_show_topics": "トピックの表示を切り替え", "zoom_in": "ズームイン", "zoom_out": "ズームアウト", "zoom_reset": "ズームをリセット"}, "theme.auto": "自動", "theme.dark": "ダーク", "theme.light": "ライト", "theme.title": "テーマ", "theme.window.style.opaque": "不透明ウィンドウ", "theme.window.style.title": "ウィンドウスタイル", "theme.window.style.transparent": "透明ウィンドウ", "title": "設定", "topic.position": "トピックの位置", "topic.position.left": "左", "topic.position.right": "右", "topic.show.time": "トピックの時間を表示", "tray.onclose": "閉じるときにトレイに最小化", "tray.show": "トレイアイコンを表示", "tray.title": "トレイ", "websearch": {"blacklist": "ブラックリスト", "blacklist_description": "以下のウェブサイトの結果は検索結果に表示されません", "check": "チェック", "check_failed": "検証に失敗しました", "check_success": "検証に成功しました", "get_api_key": "APIキーを取得", "no_provider_selected": "検索サービスプロバイダーを選択してから再確認してください。", "search_max_result": "検索結果の数", "search_provider": "検索サービスプロバイダー", "search_provider_placeholder": "検索サービスプロバイダーを選択する", "search_result_default": "デフォルト", "search_with_time": "日付を含む検索", "tavily": {"api_key": "Tavily API キー", "api_key.placeholder": "Tavily API キーを入力してください", "description": "Tavily は、AI エージェントのために特別に開発された検索エンジンで、最新の結果、インテリジェントな検索提案、そして深い研究能力を提供します", "title": "<PERSON><PERSON>"}, "title": "ウェブ検索", "blacklist_tooltip": "マッチパターン: *://*.example.com/*\n正規表現: /example\\.(net|org)/", "subscribe": "ブラックリスト購読", "subscribe_update": "更新", "subscribe_add": "サブスクリプションを追加", "subscribe_url": "フィードのURL", "subscribe_name": "代替名", "subscribe_name.placeholder": "ダウンロードしたフィードに名前がない場合に使用される代替名", "subscribe_add_success": "フィードの追加が成功しました！", "subscribe_delete": "削除", "overwrite": "サービス検索を上書き", "overwrite_tooltip": "大規模言語モデルではなく、サービス検索を使用する", "apikey": "API キー", "free": "無料", "content_limit": "内容の長さ制限", "content_limit_tooltip": "検索結果の内容長を制限し、制限を超える内容は切り捨てられます。"}, "general.auto_check_update.title": "自動更新", "quickPhrase": {"title": "クイックフレーズ", "add": "フレーズを追加", "edit": "フレーズを編集", "titleLabel": "タイトル", "contentLabel": "内容", "titlePlaceholder": "フレーズのタイトルを入力してください", "contentPlaceholder": "フレーズの内容を入力してください。変数を使用することもできます。変数を使用する場合は、Tabキーを押して変数を選択し、変数を変更してください。例：\n私の名前は${name}です。", "delete": "フレーズを削除", "deleteConfirm": "削除後は復元できません。続行しますか？"}, "quickPanel": {"title": "クイックメニュー", "close": "閉じる", "select": "選択", "page": "ページ", "confirm": "確認", "back": "戻る", "forward": "進む", "multiple": "複数選択"}, "privacy": {"title": "プライバシー設定", "enable_privacy_mode": "匿名エラーレポートとデータ統計の送信"}, "input.show_translate_confirm": "[to be translated]:显示翻译确认对话框"}, "translate": {"any.language": "任意の言語", "button.translate": "翻訳", "close": "閉じる", "confirm": {"content": "翻訳すると元のテキストが上書きされます。続行しますか？", "title": "翻訳確認"}, "error.failed": "翻訳に失敗しました", "error.not_configured": "翻訳モデルが設定されていません", "history": {"clear": "履歴をクリア", "clear_description": "履歴をクリアすると、すべての翻訳履歴が削除されます。続行しますか？", "delete": "削除", "empty": "翻訳履歴がありません", "title": "翻訳履歴"}, "input.placeholder": "翻訳するテキストを入力", "output.placeholder": "翻訳", "processing": "翻訳中...", "scroll_sync.disable": "關閉滾動同步", "scroll_sync.enable": "開啟滾動同步", "title": "翻訳", "tooltip.newline": "改行", "menu": {"description": "對當前輸入框內容進行翻譯"}}, "tray": {"quit": "終了", "show_mini_window": "クイックアシスタント", "show_window": "ウィンドウを表示"}, "words": {"knowledgeGraph": "ナレッジグラフ", "quit": "終了", "show_window": "ウィンドウを表示", "visualization": "可視化"}}}