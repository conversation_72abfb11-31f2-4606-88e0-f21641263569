appId: com.kangfenmao.CherryStudio
productName: Cherry Studio
electronLanguages:
  - zh-CN
  - zh-TW
  - en-US
  - ja # macOS/linux/win
  - ru # macOS/linux/win
  - zh_CN # for macOS
  - zh_TW # for macOS
  - en # for macOS
directories:
  buildResources: build
files:
  - '!{.vscode,.yarn,.github}'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
  - '!src'
  - '!scripts'
  - '!local'
  - '!docs'
  - '!packages'
  - '!stats.html'
  - '!*.md'
  - '!**/*.{map,ts,tsx,jsx,less,scss,sass,css.d.ts,d.cts,d.mts,md,markdown,yaml,yml}'
  - '!**/{test,tests,__tests__,coverage}/**'
  - '!**/*.{spec,test}.{js,jsx,ts,tsx}'
  - '!**/*.min.*.map'
  - '!**/*.d.ts'
  - '!**/{.DS_Store,Thumbs.db}'
  - '!**/{LICENSE,LICENSE.txt,LICENSE-MIT.txt,*.LICENSE.txt,NOTICE.txt,README.md,CHANGELOG.md}'
  - '!node_modules/rollup-plugin-visualizer'
  - '!node_modules/js-tiktoken'
  - '!node_modules/@tavily/core/node_modules/js-tiktoken'
  - '!node_modules/pdf-parse/lib/pdf.js/{v1.9.426,v1.10.88,v2.0.550}'
  - '!node_modules/mammoth/{mammoth.browser.js,mammoth.browser.min.js}'
asarUnpack:
  - resources/**
  - '**/*.{metal,exp,lib}'
win:
  executableName: Cherry Studio
  artifactName: ${productName}-${version}-${arch}-setup.${ext}
  target:
    - target: nsis
    - target: portable
nsis:
  artifactName: ${productName}-${version}-${arch}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
  allowToChangeInstallationDirectory: true
  oneClick: false
  include: build/nsis-installer.nsh
  buildUniversalInstaller: false
portable:
  artifactName: ${productName}-${version}-${arch}-portable.${ext}
mac:
  entitlementsInherit: build/entitlements.mac.plist
  notarize: false
  artifactName: ${productName}-${version}-${arch}.${ext}
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  target:
    - target: dmg
    - target: zip
linux:
  artifactName: ${productName}-${version}-${arch}.${ext}
  target:
    - target: AppImage
  maintainer: electronjs.org
  category: Utility
  desktop:
    entry:
      StartupWMClass: CherryStudio
  mimeTypes:
    - x-scheme-handler/cherrystudio
publish:
  provider: generic
  url: https://releases.cherry-ai.com
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
afterPack: scripts/after-pack.js
afterSign: scripts/notarize.js
artifactBuildCompleted: scripts/artifact-build-completed.js
releaseInfo:
  releaseNotes: |
    新增对 grok-2-image 和 gpt-4o-image 图像支持
    支持 Windows 便携版使用 data 目录存储数据
    MCP 界面改版，新增描述信息显示
    Mermaid 渲染逻辑优化
    支持关闭公示渲染
    修复 OpenAI 类型渲染错误
